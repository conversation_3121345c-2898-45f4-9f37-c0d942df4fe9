{"version": 3, "sources": ["../../assets/js/client/debug_button.js", "../../assets/js/client/highlight.js", "../../assets/js/client.js"], "sourcesContent": ["function createDebugButton(liveDebuggerURL) {\n  const debugButtonHtml = /*html*/ `\n      <div id=\"debug-button\" style=\"\n        position: fixed;\n        height: 40px;\n        width: 40px;\n        padding-left: 5px;\n        padding-right: 5px;\n        border-radius: 10px;\n        background-color: #001A72;\n        color: #ffffff;\n        display: flex;\n        gap: 5px;\n        justify-content: center;\n        align-items: center;\n        z-index: 9999;\n        bottom: 20px;\n        right: 20px;\n        cursor: grab;\">\n        <a href=\"${liveDebuggerURL}\" target=\"_blank\">\n          <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\"  fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.0941 20.624C22.5697 20.624 22.9553 20.2385 22.9554 19.7628L22.9556 16.6568C22.9556 16.4283 22.8649 16.2093 22.7034 16.0477C22.5418 15.8862 22.3228 15.7955 22.0944 15.7955L18.3034 15.7955L18.3034 17.5179L21.2331 17.5179L21.2329 19.7627C21.2329 20.2384 21.6185 20.624 22.0941 20.624Z\" fill=\"currentColor\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.9823 12.9677C22.9823 12.4921 22.5968 12.1065 22.1211 12.1065L18.3034 12.1065V13.8289H22.1211C22.5968 13.8289 22.9823 13.4433 22.9823 12.9677Z\" fill=\"currentColor\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.1385 5.31162C22.6142 5.31159 22.9998 5.69715 22.9998 6.17279L23 9.27886C23 9.50728 22.9093 9.72635 22.7478 9.88787C22.5863 10.0494 22.3672 10.1401 22.1388 10.1401L18.3034 10.1402L18.3164 8.41772L21.2775 8.4177L21.2774 6.1729C21.2773 5.69726 21.6629 5.31165 22.1385 5.31162Z\" fill=\"currentColor\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.86148 20.624C1.38585 20.624 1.00024 20.2385 1.00021 19.7628L1 16.6568C0.999985 16.4283 1.09071 16.2093 1.25222 16.0478C1.41373 15.8862 1.6328 15.7955 1.86122 15.7955L5.6836 15.7955L5.6836 17.5179L2.7225 17.5179L2.72265 19.7627C2.72268 20.2384 2.33712 20.624 1.86148 20.624Z\" fill=\"currentColor\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.00465 12.9677C1.00465 12.4921 1.39023 12.1065 1.86587 12.1065L5.6836 12.1065L5.68361 13.8289L1.86588 13.8289C1.39024 13.8289 1.00465 13.4434 1.00465 12.9677Z\" fill=\"currentColor\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.8619 5.31162C1.38626 5.31159 1.00066 5.69715 1.00062 6.17279L1.00042 9.27886C1.00041 9.50728 1.09113 9.72635 1.25265 9.88787C1.41416 10.0494 1.63322 10.1401 1.86164 10.1401L5.68402 10.1402L5.68403 8.41773L2.72292 8.4177L2.72307 6.1729C2.7231 5.69726 2.33754 5.31165 1.8619 5.31162Z\" fill=\"currentColor\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.5822 5.951C16.5563 5.55988 16.4815 5.18216 16.3637 4.82389C15.761 2.9901 14.0347 1.66608 11.9993 1.66608C9.46255 1.66608 7.40614 3.72245 7.40607 6.25914C7.40607 6.07964 7.41637 5.90255 7.4364 5.72842C6.37344 6.4641 5.5006 7.27955 4.95972 7.82907C4.57693 8.21798 4.3604 8.47369 4.3604 8.47369V15.576C4.3604 16.9134 4.75938 18.2352 5.70216 19.1839C6.91489 20.4041 8.85964 21.9501 11.1606 22.2731C11.4426 22.3127 11.7299 22.3339 12.0219 22.3339C12.3121 22.3339 12.5995 22.3101 12.8831 22.266C15.0234 21.9325 16.9448 20.4369 18.1856 19.2336C19.1941 18.2556 19.6354 16.8718 19.6354 15.4671V8.47369C19.6354 8.47369 19.4208 8.21698 19.0408 7.8268C18.5007 7.27223 17.6266 6.44804 16.5597 5.7081C16.5693 5.78844 16.5768 5.86943 16.5822 5.951ZM9.94789 6.25926C9.21504 6.59448 8.48065 7.06772 7.79147 7.60478C7.27937 8.00384 6.83377 8.40537 6.483 8.74437L12.0205 12.0717L17.5232 8.74442C17.1757 8.40464 16.7328 8.00128 16.2228 7.60045C15.5416 7.06516 14.8151 6.59357 14.0886 6.25926H9.94789ZM11.9993 3.38852C13.0618 3.38852 13.9896 3.96582 14.4859 4.82389L9.51244 4.82411C10.0088 3.96592 10.9366 3.38852 11.9993 3.38852ZM11.1606 13.5645L6.08284 10.5134V15.576C6.08284 16.5949 6.38663 17.4291 6.92389 17.9697C8.02405 19.0767 9.53264 20.2139 11.1606 20.5272V13.5645ZM12.8831 20.5143C14.3643 20.1903 15.8444 19.1047 16.9865 17.9971C17.5787 17.4228 17.913 16.5336 17.913 15.4671V10.5216L12.8831 13.5629V20.5143Z\" fill=\"currentColor\"/>\n          </svg>\n        </a>\n      </div>\n  `;\n\n  const tempDiv = document.createElement('div');\n  tempDiv.innerHTML = debugButtonHtml;\n\n  return tempDiv.firstElementChild;\n}\n\nfunction initDebugButton(liveDebuggerURL) {\n  const debugButton = createDebugButton(liveDebuggerURL);\n  document.body.appendChild(debugButton);\n\n  let dragging = false;\n\n  const onMouseDown = (event) => {\n    if (event.button !== 0 || event.ctrlKey) return;\n    event.preventDefault();\n    posXStart = event.clientX;\n    posYStart = event.clientY;\n    document.addEventListener('mousemove', onMouseMove);\n    document.addEventListener('mouseup', onMouseUp);\n    debugButton.style.cursor = 'grabbing';\n    dragging = false;\n  };\n\n  const onMouseMove = (event) => {\n    if (!event.clientX || !event.clientY) return;\n    dragging = true;\n    posX = posXStart - event.clientX;\n    posY = posYStart - event.clientY;\n    posXStart = event.clientX;\n    posYStart = event.clientY;\n    debugButton.style.top = `${debugButton.offsetTop - posY}px`;\n    debugButton.style.left = `${debugButton.offsetLeft - posX}px`;\n  };\n\n  const onMouseUp = () => {\n    document.removeEventListener('mousemove', onMouseMove);\n    document.removeEventListener('mouseup', onMouseUp);\n    debugButton.style.cursor = 'grab';\n\n    if (debugButton.offsetTop < 0) {\n      debugButton.style.top = debugButton.style.bottom;\n    }\n    if (debugButton.offsetTop + debugButton.clientHeight > window.innerHeight) {\n      debugButton.style.top = '';\n    }\n    if (debugButton.offsetLeft < 0) {\n      debugButton.style.left = debugButton.style.right;\n    }\n    if (debugButton.offsetLeft + debugButton.clientWidth > window.innerWidth) {\n      debugButton.style.left = '';\n    }\n  };\n\n  const onClick = (event) => {\n    if (dragging) {\n      event.preventDefault();\n      dragging = false;\n    }\n  };\n\n  window.addEventListener('resize', () => {\n    if (\n      debugButton.offsetLeft +\n        debugButton.clientWidth +\n        Number.parseInt(debugButton.style.right) >\n      window.innerWidth\n    ) {\n      debugButton.style.left = '';\n    }\n    if (\n      debugButton.offsetTop +\n        debugButton.clientHeight +\n        Number.parseInt(debugButton.style.bottom) >\n      window.innerHeight\n    ) {\n      debugButton.style.top = '';\n    }\n  });\n\n  debugButton.addEventListener('mousedown', onMouseDown);\n  debugButton.addEventListener('click', onClick);\n}\n\nexport { initDebugButton };\n", "const highlightElementID = 'live-debugger-highlight-element';\nconst highlightPulseElementID = 'live-debugger-highlight-pulse-element';\n\nconst isElementVisible = (element) => {\n  if (!element) return false;\n\n  const style = window.getComputedStyle(element);\n  return (\n    style.display !== 'none' &&\n    style.visibility !== 'hidden' &&\n    style.opacity !== '0'\n  );\n};\n\nfunction createHighlightElement(activeElement, detail, id) {\n  const rect = activeElement.getBoundingClientRect();\n  const highlight = document.createElement('div');\n\n  highlight.id = id;\n  highlight.dataset.attr = detail.attr;\n  highlight.dataset.val = detail.val;\n\n  highlight.style.position = 'absolute';\n  highlight.style.top = `${rect.top + window.scrollY}px`;\n  highlight.style.left = `${rect.left + window.scrollX}px`;\n  highlight.style.width = `${activeElement.offsetWidth}px`;\n  highlight.style.height = `${activeElement.offsetHeight}px`;\n  highlight.style.backgroundColor = '#87CCE880';\n  highlight.style.zIndex = '10000';\n  highlight.style.pointerEvents = 'none';\n\n  return highlight;\n}\n\nfunction handleHighlight({ detail }) {\n  let highlightElement = document.getElementById(highlightElementID);\n\n  if (highlightElement) {\n    highlightElement.remove();\n\n    const toClear = detail.attr === undefined || detail.val === undefined;\n    const sameElement = highlightElement.dataset.val === detail.val;\n\n    if (toClear || sameElement) {\n      return;\n    }\n  }\n\n  const activeElement = document.querySelector(\n    `[${detail.attr}=\"${detail.val}\"]`\n  );\n\n  if (isElementVisible(activeElement)) {\n    highlightElement = createHighlightElement(\n      activeElement,\n      detail,\n      highlightElementID\n    );\n    document.body.appendChild(highlightElement);\n  }\n}\n\nfunction handleHighlightResize() {\n  const highlight = document.getElementById(highlightElementID);\n  if (highlight) {\n    const activeElement = document.querySelector(\n      `[${highlight.dataset.attr}=\"${highlight.dataset.val}\"]`\n    );\n    const rect = activeElement.getBoundingClientRect();\n\n    highlight.style.top = `${rect.top + window.scrollY}px`;\n    highlight.style.left = `${rect.left + window.scrollX}px`;\n    highlight.style.width = `${activeElement.offsetWidth}px`;\n    highlight.style.height = `${activeElement.offsetHeight}px`;\n  }\n}\n\nfunction handlePulse({ detail }) {\n  const activeElement = document.querySelector(\n    `[${detail.attr}=\"${detail.val}\"]`\n  );\n\n  if (isElementVisible(activeElement)) {\n    const highlightPulse = createHighlightElement(\n      activeElement,\n      detail,\n      highlightPulseElementID\n    );\n\n    document.body.appendChild(highlightPulse);\n\n    const w = highlightPulse.offsetWidth;\n    const h = highlightPulse.offsetHeight;\n\n    highlightPulse.animate(\n      [\n        {\n          width: `${w}px`,\n          height: `${h}px`,\n          transform: 'translate(0, 0)',\n          backgroundColor: '#87CCE860',\n        },\n        {\n          width: `${w + 20}px`,\n          height: `${h + 20}px`,\n          transform: 'translate(-10px, -10px)',\n          backgroundColor: '#87CCE830',\n        },\n        {\n          width: `${w + 40}px`,\n          height: `${h + 40}px`,\n          transform: 'translate(-20px, -20px)',\n          backgroundColor: '#87CCE800',\n        },\n      ],\n      {\n        duration: 500,\n        iterations: 1,\n        delay: 200,\n      }\n    ).onfinish = () => {\n      highlightPulse.remove();\n    };\n  }\n}\n\nfunction initHighlight() {\n  window.addEventListener('phx:highlight', handleHighlight);\n  window.addEventListener('resize', handleHighlightResize);\n  window.addEventListener('phx:pulse', handlePulse);\n}\n\nexport { initHighlight };\n", "// This file is being run in the client's debugged application\n// It introduces browser features that are not mandatory for LiveDebugger to run\n\nimport { initDebugButton } from './client/debug_button';\nimport { initHighlight } from './client/highlight';\n\n// Fetch LiveDebugger URL\nfunction getSessionId() {\n  let el;\n  if ((el = document.querySelector('[data-phx-main]'))) {\n    return el.id;\n  }\n  if ((el = document.querySelector('[id^=\"phx-\"]'))) {\n    return el.id;\n  }\n  if ((el = document.querySelector('[data-phx-root-id]'))) {\n    return el.getAttribute('data-phx-root-id');\n  }\n}\n\nfunction handleMetaTagError() {\n  const message = `\n  LiveDebugger meta tag not found!\n  If you have recently bumped LiveDebugger version, please update your layout according to the instructions in the GitHub README.\n  You can find it here: https://github.com/software-mansion/live-debugger#installation\n  `;\n\n  throw new Error(message);\n}\n\nfunction debugButtonEnabled() {\n  const metaTag = document.querySelector('meta[name=\"live-debugger-config\"]');\n\n  if (metaTag) {\n    return metaTag.hasAttribute('debug-button');\n  } else {\n    handleMetaTagError();\n  }\n}\n\nfunction highlightingEnabled() {\n  const metaTag = document.querySelector('meta[name=\"live-debugger-config\"]');\n\n  if (metaTag) {\n    return metaTag.hasAttribute('highlighting');\n  }\n}\n\nfunction getLiveDebuggerBaseURL() {\n  const metaTag = document.querySelector('meta[name=\"live-debugger-config\"]');\n\n  if (metaTag) {\n    return metaTag.getAttribute('url');\n  } else {\n    handleMetaTagError();\n  }\n}\n\nfunction getSessionURL(baseURL) {\n  const session_id = getSessionId();\n  const session_path = session_id ? `transport_pid/${session_id}` : '';\n\n  return `${baseURL}/${session_path}`;\n}\n\nwindow.document.addEventListener('DOMContentLoaded', function () {\n  const baseURL = getLiveDebuggerBaseURL();\n  const sessionURL = getSessionURL(baseURL);\n\n  if (debugButtonEnabled()) {\n    initDebugButton(sessionURL);\n  }\n\n  if (highlightingEnabled()) {\n    initHighlight();\n  }\n\n  // Finalize\n  console.info(`LiveDebugger available at: ${baseURL}`);\n});\n"], "mappings": "MAAA,SAASA,EAAkBC,EAAiB,CAC1C,IAAMC,EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAkBhBD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcXE,EAAU,SAAS,cAAc,KAAK,EAC5C,OAAAA,EAAQ,UAAYD,EAEbC,EAAQ,iBACjB,CAEA,SAASC,EAAgBH,EAAiB,CACxC,IAAMI,EAAcL,EAAkBC,CAAe,EACrD,SAAS,KAAK,YAAYI,CAAW,EAErC,IAAIC,EAAW,GAETC,EAAeC,GAAU,CACzBA,EAAM,SAAW,GAAKA,EAAM,UAChCA,EAAM,eAAe,EACrB,UAAYA,EAAM,QAClB,UAAYA,EAAM,QAClB,SAAS,iBAAiB,YAAaC,CAAW,EAClD,SAAS,iBAAiB,UAAWC,CAAS,EAC9CL,EAAY,MAAM,OAAS,WAC3BC,EAAW,GACb,EAEMG,EAAeD,GAAU,CACzB,CAACA,EAAM,SAAW,CAACA,EAAM,UAC7BF,EAAW,GACX,KAAO,UAAYE,EAAM,QACzB,KAAO,UAAYA,EAAM,QACzB,UAAYA,EAAM,QAClB,UAAYA,EAAM,QAClBH,EAAY,MAAM,IAAM,GAAGA,EAAY,UAAY,SACnDA,EAAY,MAAM,KAAO,GAAGA,EAAY,WAAa,SACvD,EAEMK,EAAY,IAAM,CACtB,SAAS,oBAAoB,YAAaD,CAAW,EACrD,SAAS,oBAAoB,UAAWC,CAAS,EACjDL,EAAY,MAAM,OAAS,OAEvBA,EAAY,UAAY,IAC1BA,EAAY,MAAM,IAAMA,EAAY,MAAM,QAExCA,EAAY,UAAYA,EAAY,aAAe,OAAO,cAC5DA,EAAY,MAAM,IAAM,IAEtBA,EAAY,WAAa,IAC3BA,EAAY,MAAM,KAAOA,EAAY,MAAM,OAEzCA,EAAY,WAAaA,EAAY,YAAc,OAAO,aAC5DA,EAAY,MAAM,KAAO,GAE7B,EAEMM,EAAWH,GAAU,CACrBF,IACFE,EAAM,eAAe,EACrBF,EAAW,GAEf,EAEA,OAAO,iBAAiB,SAAU,IAAM,CAEpCD,EAAY,WACVA,EAAY,YACZ,OAAO,SAASA,EAAY,MAAM,KAAK,EACzC,OAAO,aAEPA,EAAY,MAAM,KAAO,IAGzBA,EAAY,UACVA,EAAY,aACZ,OAAO,SAASA,EAAY,MAAM,MAAM,EAC1C,OAAO,cAEPA,EAAY,MAAM,IAAM,GAE5B,CAAC,EAEDA,EAAY,iBAAiB,YAAaE,CAAW,EACrDF,EAAY,iBAAiB,QAASM,CAAO,CAC/C,CClHA,IAAMC,EAAqB,kCACrBC,EAA0B,wCAE1BC,EAAoBC,GAAY,CACpC,GAAI,CAACA,EAAS,MAAO,GAErB,IAAMC,EAAQ,OAAO,iBAAiBD,CAAO,EAC7C,OACEC,EAAM,UAAY,QAClBA,EAAM,aAAe,UACrBA,EAAM,UAAY,GAEtB,EAEA,SAASC,EAAuBC,EAAeC,EAAQC,EAAI,CACzD,IAAMC,EAAOH,EAAc,sBAAsB,EAC3CI,EAAY,SAAS,cAAc,KAAK,EAE9C,OAAAA,EAAU,GAAKF,EACfE,EAAU,QAAQ,KAAOH,EAAO,KAChCG,EAAU,QAAQ,IAAMH,EAAO,IAE/BG,EAAU,MAAM,SAAW,WAC3BA,EAAU,MAAM,IAAM,GAAGD,EAAK,IAAM,OAAO,YAC3CC,EAAU,MAAM,KAAO,GAAGD,EAAK,KAAO,OAAO,YAC7CC,EAAU,MAAM,MAAQ,GAAGJ,EAAc,gBACzCI,EAAU,MAAM,OAAS,GAAGJ,EAAc,iBAC1CI,EAAU,MAAM,gBAAkB,YAClCA,EAAU,MAAM,OAAS,QACzBA,EAAU,MAAM,cAAgB,OAEzBA,CACT,CAEA,SAASC,EAAgB,CAAE,OAAAJ,CAAO,EAAG,CACnC,IAAIK,EAAmB,SAAS,eAAeZ,CAAkB,EAEjE,GAAIY,EAAkB,CACpBA,EAAiB,OAAO,EAExB,IAAMC,EAAUN,EAAO,OAAS,QAAaA,EAAO,MAAQ,OACtDO,EAAcF,EAAiB,QAAQ,MAAQL,EAAO,IAE5D,GAAIM,GAAWC,EACb,MAEJ,CAEA,IAAMR,EAAgB,SAAS,cAC7B,IAAIC,EAAO,SAASA,EAAO,OAC7B,EAEIL,EAAiBI,CAAa,IAChCM,EAAmBP,EACjBC,EACAC,EACAP,CACF,EACA,SAAS,KAAK,YAAYY,CAAgB,EAE9C,CAEA,SAASG,GAAwB,CAC/B,IAAML,EAAY,SAAS,eAAeV,CAAkB,EAC5D,GAAIU,EAAW,CACb,IAAMJ,EAAgB,SAAS,cAC7B,IAAII,EAAU,QAAQ,SAASA,EAAU,QAAQ,OACnD,EACMD,EAAOH,EAAc,sBAAsB,EAEjDI,EAAU,MAAM,IAAM,GAAGD,EAAK,IAAM,OAAO,YAC3CC,EAAU,MAAM,KAAO,GAAGD,EAAK,KAAO,OAAO,YAC7CC,EAAU,MAAM,MAAQ,GAAGJ,EAAc,gBACzCI,EAAU,MAAM,OAAS,GAAGJ,EAAc,gBAC5C,CACF,CAEA,SAASU,EAAY,CAAE,OAAAT,CAAO,EAAG,CAC/B,IAAMD,EAAgB,SAAS,cAC7B,IAAIC,EAAO,SAASA,EAAO,OAC7B,EAEA,GAAIL,EAAiBI,CAAa,EAAG,CACnC,IAAMW,EAAiBZ,EACrBC,EACAC,EACAN,CACF,EAEA,SAAS,KAAK,YAAYgB,CAAc,EAExC,IAAMC,EAAID,EAAe,YACnBE,EAAIF,EAAe,aAEzBA,EAAe,QACb,CACE,CACE,MAAO,GAAGC,MACV,OAAQ,GAAGC,MACX,UAAW,kBACX,gBAAiB,WACnB,EACA,CACE,MAAO,GAAGD,EAAI,OACd,OAAQ,GAAGC,EAAI,OACf,UAAW,0BACX,gBAAiB,WACnB,EACA,CACE,MAAO,GAAGD,EAAI,OACd,OAAQ,GAAGC,EAAI,OACf,UAAW,0BACX,gBAAiB,WACnB,CACF,EACA,CACE,SAAU,IACV,WAAY,EACZ,MAAO,GACT,CACF,EAAE,SAAW,IAAM,CACjBF,EAAe,OAAO,CACxB,CACF,CACF,CAEA,SAASG,GAAgB,CACvB,OAAO,iBAAiB,gBAAiBT,CAAe,EACxD,OAAO,iBAAiB,SAAUI,CAAqB,EACvD,OAAO,iBAAiB,YAAaC,CAAW,CAClD,CC3HA,SAASK,GAAe,CACtB,IAAIC,EAIJ,IAHKA,EAAK,SAAS,cAAc,iBAAiB,KAG7CA,EAAK,SAAS,cAAc,cAAc,GAC7C,OAAOA,EAAG,GAEZ,GAAKA,EAAK,SAAS,cAAc,oBAAoB,EACnD,OAAOA,EAAG,aAAa,kBAAkB,CAE7C,CAEA,SAASC,GAAqB,CAC5B,IAAMC,EAAU;AAAA;AAAA;AAAA;AAAA,IAMhB,MAAM,IAAI,MAAMA,CAAO,CACzB,CAEA,SAASC,GAAqB,CAC5B,IAAMC,EAAU,SAAS,cAAc,mCAAmC,EAE1E,GAAIA,EACF,OAAOA,EAAQ,aAAa,cAAc,EAE1CH,EAAmB,CAEvB,CAEA,SAASI,GAAsB,CAC7B,IAAMD,EAAU,SAAS,cAAc,mCAAmC,EAE1E,GAAIA,EACF,OAAOA,EAAQ,aAAa,cAAc,CAE9C,CAEA,SAASE,GAAyB,CAChC,IAAMF,EAAU,SAAS,cAAc,mCAAmC,EAE1E,GAAIA,EACF,OAAOA,EAAQ,aAAa,KAAK,EAEjCH,EAAmB,CAEvB,CAEA,SAASM,EAAcC,EAAS,CAC9B,IAAMC,EAAaV,EAAa,EAC1BW,EAAeD,EAAa,iBAAiBA,IAAe,GAElE,MAAO,GAAGD,KAAWE,GACvB,CAEA,OAAO,SAAS,iBAAiB,mBAAoB,UAAY,CAC/D,IAAMF,EAAUF,EAAuB,EACjCK,EAAaJ,EAAcC,CAAO,EAEpCL,EAAmB,GACrBS,EAAgBD,CAAU,EAGxBN,EAAoB,GACtBQ,EAAc,EAIhB,QAAQ,KAAK,8BAA8BL,GAAS,CACtD,CAAC", "names": ["createDebugButton", "liveDebuggerURL", "debugButtonHtml", "tempDiv", "initDebugButton", "debugButton", "dragging", "onMouseDown", "event", "onMouseMove", "onMouseUp", "onClick", "highlightElementID", "highlightPulseElementID", "isElementVisible", "element", "style", "createHighlightElement", "activeElement", "detail", "id", "rect", "highlight", "handleHighlight", "highlightElement", "toClear", "sameElement", "handleHighlightResize", "handlePulse", "highlightPulse", "w", "h", "initHighlight", "getSessionId", "el", "handleMetaTagError", "message", "debugButtonEnabled", "metaTag", "highlightingEnabled", "getLiveDebuggerBaseURL", "getSessionURL", "baseURL", "session_id", "session_path", "sessionURL", "initDebugButton", "initHighlight"]}