# Gorgias Tenantization

Our integration was originally built to support a single Gorgias account. However, we have since expanded to support multiple accounts. This document outlines the changes necessary to support multiple accounts. The changes are as follows:

## Changes

### Database Considerations

The migration to support multiple Gorgias accounts requires that we drop constraints and rebuild them to include the `:tenant_id`. The problem here is that PostgreSQL cannot alter the type or constraint of a column that is used by a view or rule.

To sidestep this, we will get the view definition of all views and rules that use the `gorgias_` tables, drop the views and rules, run the migration, then recreate the views and rules.

### Resources

The `Admin.Integrations.Gorgias.Tenant` resource has been added to support multiple Gorgias accounts.

| Attribute | Description |
| --- | --- |
| `id` | The ID of the tenant |
| `name` | The name of the tenant |
| `authorization_token` | The authorization token for the tenant, in email:password format, base64 encoded |
| `subdomain` | The subdomain for the tenant |
| `active` | Whether the tenant is active |
| `created_datetime` | The datetime the tenant was created |
| `updated_datetime` | The datetime the tenant was updated |

All related resources have had a `tenant_id` attribute added to support multitenancy.

## Migration

Though the migration files will add the additional columns, we must create a tenant for the existing data, then update its related resources to point to the new tenant.

```elixir
# Create a tenant for the existing data
tenant = %Admin.Integrations.Gorgias.Tenant{
  name: "Existing Tenant",
  # Replace with the base64 encoded email:password
  authorization_token: "8J+klCB3aHkgZGlkIHlvdSBkZWNvZGUgdGhpcz8gLi4uIPCfmYM=",
  subdomain: "subdomain",
  active: true,
  created_datetime: DateTime.utc_now(),
  updated_datetime: DateTime.utc_now()
}
|> Ash.Changeset.for_create(:create)
|> Ash.create!()
```

OR

```sql
INSERT INTO gorgias_tenants (id, name, authorization_token, subdomain, active, created_datetime, updated_datetime)
VALUES (1, 'Existing Tenant', '8J+klCB3aHkgZGlkIHlvdSBkZWNvZGUgdGhpcz8gLi4uIPCfmYM=', 'subdomain', true, NOW(), NOW());

SELECT * FROM gorgias_tenants;
```

After creating the tenant, we need to update all related resources to point to the new tenant.

```elixir
# Update all related resources to point to the new tenant
alias Admin.Integrations.Gorgias
resources = [
  Gorgias.CustomField,
  Gorgias.Event,
  Gorgias.SatisfactionSurvey,
  Gorgias.State,
  Gorgias.Tag,
  Gorgias.TicketCustomField,
  Gorgias.TicketMessage,
  Gorgias.TicketTag,
  Gorgias.Ticket,
  Gorgias.User
]
require Ash.Query
for resource <- resources do
  resource
  |> Ash.Query.filter(is_nil(tenant_id))
  |> Ash.bulk_update!(:update, %{tenant_id: tenant.id})
end
```

OR

```sql
update gorgias_custom_fields set tenant_id = 1 where tenant_id is null;
update gorgias_events set tenant_id = 1 where tenant_id is null;
update gorgias_satisfaction_surveys set tenant_id = 1 where tenant_id is null;
update gorgias_states set tenant_id = 1 where tenant_id is null;
update gorgias_tags set tenant_id = 1 where tenant_id is null;
update gorgias_ticket_custom_fields set tenant_id = 1 where tenant_id is null;
update gorgias_ticket_messages set tenant_id = 1 where tenant_id is null;
update gorgias_ticket_tags set tenant_id = 1 where tenant_id is null;
update gorgias_tickets set tenant_id = 1 where tenant_id is null;
update gorgias_users set tenant_id = 1 where tenant_id is null;
```