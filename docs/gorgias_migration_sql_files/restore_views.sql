create or replace view metrics.chosen_gift_msas("MSA/CBSA name", zip_count, orderamt, ordercount) as
WITH extractedzip AS (
    SELECT chosendemosfull2."Customer ID",
           "left"(replace(chosendemosfull2."Zip", ''''::text, ''::text), 5) AS zip5
    FROM metrics.chosendemosfull2
)
SELECT g."MSA/CBSA name",
       count(c.zip5) AS zip_count,
       sum(o.net_payment_set) - sum(o.total_refunded_shipping_set) AS orderamt,
       count(o.order_id) AS ordercount
FROM metrics."General MSA List" g
         JOIN extractedzip c ON lpad(g."ZIP (zcta5)", 5, '0'::text) = c.zip5
         LEFT JOIN metrics.shopify_customers p ON c."Customer ID" = p.customer_id
         LEFT JOIN metrics.shopify_orders o ON p.shopify_id = o.customer_id
WHERE date(o.created_at_america_chicago) >= '2023-08-01'::date
GROUP BY g."MSA/CBSA name";

alter table metrics.chosen_gift_msas
    owner to postgres;

create or replace view metrics.gorgias_tickets_handled(user_id, date, "Tickets Handled", email) as
SELECT e.user_id,
    date(e.created_mountain_datetime) AS date,
    count(DISTINCT e.object_id) AS "Tickets Handled",
    gu.email
FROM metrics.gorgias_events e
    LEFT JOIN metrics.gorgias_users gu ON e.user_id = gu.id
WHERE e.created_mountain_datetime >= '2024-09-01 00:00:00'::timestamp without time zone AND e.created_mountain_datetime <= CURRENT_DATE AND e.object_type = 'Ticket'::text AND e.user_id IS NOT NULL
GROUP BY e.user_id, gu.email, (date(e.created_mountain_datetime));

alter table metrics.gorgias_tickets_handled
    owner to postgres;

create or replace view metrics.distinct_gorgias_users_2024(user_id, email, date) as
SELECT DISTINCT user_id,
                email,
    date
FROM metrics.gorgias_tickets_handled t
WHERE date_part('year'::text, date) = 2024::double precision AND email IS NOT NULL;

alter table metrics.distinct_gorgias_users_2024
    owner to postgres;

create or replace view metrics.gorgias_closed_events(object_id, type, user_id, first_close, email) as
SELECT e.object_id,
       e.type,
       e.user_id,
       min(e.created_mountain_datetime) AS first_close,
       gu.email
FROM metrics.gorgias_events e
         LEFT JOIN metrics.gorgias_users gu ON e.user_id = gu.id
WHERE e.created_mountain_datetime >= '2024-01-01 00:00:00'::timestamp without time zone AND e.created_mountain_datetime <= CURRENT_DATE AND e.type = 'ticket-closed'::text AND e.user_id IS NOT NULL
GROUP BY e.object_id, e.type, e.user_id, gu.email;

alter table metrics.gorgias_closed_events
    owner to postgres;

create or replace view metrics.gorgias_closed_events2(user_id, email, date, tickets_closed) as
SELECT e.user_id,
       gu.email,
    date(min(e.created_mountain_datetime)) AS date,
    count(e.object_id) AS tickets_closed
FROM metrics.gorgias_events e
    LEFT JOIN metrics.gorgias_users gu ON e.user_id = gu.id
WHERE e.created_mountain_datetime >= '2024-01-01 00:00:00'::timestamp without time zone AND e.created_mountain_datetime <= CURRENT_DATE AND e.type = 'ticket-closed'::text AND e.user_id IS NOT NULL
GROUP BY e.user_id, gu.email, (date(e.created_mountain_datetime));

alter table metrics.gorgias_closed_events2
    owner to postgres;

create or replace view metrics.gorgias_events2
            (id, context, channel, created_datetime, data, object_id, object_type, type, user_id, uri,
             created_mountain_datetime, within_work_hours, created_ameri_chicago_datetime)
as
SELECT id,
       context,
       channel,
       created_datetime,
       data,
       object_id,
       object_type,
       type,
       user_id,
       uri,
       created_mountain_datetime,
       within_work_hours,
       created_ameri_chicago_datetime
FROM metrics.gorgias_events
WHERE created_ameri_chicago_datetime >= '2024-01-01 00:00:00'::timestamp without time zone AND created_ameri_chicago_datetime <= now();

alter table metrics.gorgias_events2
    owner to postgres;

create or replace view metrics.gorgias_events_app
            (id, context, channel, created_datetime, data, object_id, object_type, type, user_id, uri,
             within_work_hours, created_ameri_chicago_datetime, created_mountain_datetime)
as
SELECT id,
       context,
       channel,
       created_datetime,
       data,
       object_id,
       object_type,
       type,
       user_id,
       uri,
       within_work_hours,
       created_ameri_chicago_datetime,
       created_mountain_datetime
FROM metrics.gorgias_events
WHERE created_ameri_chicago_datetime >= '2024-01-01 00:00:00'::timestamp without time zone AND created_ameri_chicago_datetime <= now();

alter table metrics.gorgias_events_app
    owner to postgres;

create or replace view metrics.gorgias_tickets2
            (id, channel, opened_datetime, created_datetime, updated_datetime, trashed_datetime, closed_datetime,
             snooze_datetime, external_id, meta, spam, subject, status, via, uri, satisfaction_survey, events_fetched,
             shopify_order_id, shopify_order_name, assignee_team, next_opening, bh_first_auto_response_datetime,
             bh_first_human_response_datetime, bh_first_close_datetime, gorgias_customer_id, shopify_customer_id,
             shopify_customer_id_looked_up, bh_days_diff, bh_tcreated_mountain, bh_next_opening, bh_fhrt_secs,
             bh_fart_secs, created_ameri_chicago_datetime, created_mountain_datetime)
as
SELECT id,
       channel,
       opened_datetime,
       created_datetime,
       updated_datetime,
       trashed_datetime,
       closed_datetime,
       snooze_datetime,
       external_id,
       meta,
       spam,
       subject,
       status,
       via,
       uri,
       satisfaction_survey,
       events_fetched,
       shopify_order_id,
       shopify_order_name,
       assignee_team,
       next_opening,
       bh_first_auto_response_datetime,
       bh_first_human_response_datetime,
       bh_first_close_datetime,
       gorgias_customer_id,
       shopify_customer_id,
       shopify_customer_id_looked_up,
       bh_days_diff,
       bh_tcreated_mountain,
       bh_next_opening,
       bh_fhrt_secs,
       bh_fart_secs,
       created_ameri_chicago_datetime,
       created_mountain_datetime
FROM metrics.gorgias_tickets
WHERE created_ameri_chicago_datetime >= '2024-01-01 00:00:00'::timestamp without time zone AND created_ameri_chicago_datetime <= now();

alter table metrics.gorgias_tickets2
    owner to postgres;

create or replace view metrics.gorgias_tickets_app
            (id, channel, opened_datetime, created_datetime, updated_datetime, trashed_datetime, closed_datetime,
             snooze_datetime, external_id, meta, spam, subject, status, via, uri, satisfaction_survey, events_fetched,
             shopify_order_id, shopify_order_name, assignee_team, next_opening, bh_first_auto_response_datetime,
             bh_first_human_response_datetime, bh_first_close_datetime, gorgias_customer_id, shopify_customer_id,
             shopify_customer_id_looked_up, bh_days_diff, bh_tcreated_mountain, bh_next_opening, bh_fhrt_secs,
             bh_fart_secs, created_ameri_chicago_datetime, created_mountain_datetime)
as
SELECT id,
       channel,
       opened_datetime,
       created_datetime,
       updated_datetime,
       trashed_datetime,
       closed_datetime,
       snooze_datetime,
       external_id,
       meta,
       spam,
       subject,
       status,
       via,
       uri,
       satisfaction_survey,
       events_fetched,
       shopify_order_id,
       shopify_order_name,
       assignee_team,
       next_opening,
       bh_first_auto_response_datetime,
       bh_first_human_response_datetime,
       bh_first_close_datetime,
       gorgias_customer_id,
       shopify_customer_id,
       shopify_customer_id_looked_up,
       bh_days_diff,
       bh_tcreated_mountain,
       bh_next_opening,
       bh_fhrt_secs,
       bh_fart_secs,
       created_ameri_chicago_datetime,
       created_mountain_datetime
FROM metrics.gorgias_tickets
WHERE created_ameri_chicago_datetime >= '2024-01-01 00:00:00'::timestamp without time zone AND created_ameri_chicago_datetime <= now();

alter table metrics.gorgias_tickets_app
    owner to postgres;

create or replace view metrics.min_user_eventdate
            (ticket_id, ticket_msg_id, spam, msg_createdtime, msg_senttime, from_agent, via, channel,
             ticket_createdtime, min, ticket_next_opening, ticket_created_in_hours, min_created_in_hours)
as
WITH ticket_msgs AS (
    SELECT rank() OVER (PARTITION BY gorgias_ticket_messages.ticket_id ORDER BY gorgias_ticket_messages.id) AS rank,
           gorgias_ticket_messages.id,
           gorgias_ticket_messages.subject,
           gorgias_ticket_messages.body_text,
           gorgias_ticket_messages.channel,
           timezone('mst'::text, timezone('utc'::text, gorgias_ticket_messages.created_datetime)) AS msg_created_mountain_datetime,
           gorgias_ticket_messages.failed_datetime,
           gorgias_ticket_messages.sent_datetime,
           timezone('mst'::text, timezone('utc'::text, gorgias_ticket_messages.sent_datetime)) AS msg_sent_mountain_datetime,
           gorgias_ticket_messages.from_agent,
           gorgias_ticket_messages.ticket_id,
           gorgias_ticket_messages.via,
           gorgias_ticket_messages.uri,
           date_part('isodow'::text, timezone('mst'::text, timezone('utc'::text, gorgias_ticket_messages.sent_datetime))) AS dow,
           date_part('hour'::text, timezone('mst'::text, timezone('utc'::text, gorgias_ticket_messages.sent_datetime))) AS hour
    FROM metrics.gorgias_ticket_messages
    WHERE gorgias_ticket_messages.from_agent = true AND (gorgias_ticket_messages.via <> ALL (ARRAY['internal-note'::text, 'rule'::text]))
), tickets AS (
    SELECT gorgias_tickets.id,
           gorgias_tickets.spam,
           gorgias_tickets.trashed_datetime,
           timezone('mst'::text, timezone('utc'::text, gorgias_tickets.created_datetime)) AS created_mountain_datetime,
           date_part('isodow'::text, timezone('mst'::text, timezone('utc'::text, gorgias_tickets.created_datetime))) AS dow,
           date_part('hour'::text, timezone('mst'::text, timezone('utc'::text, gorgias_tickets.created_datetime))) AS hour
    FROM metrics.gorgias_tickets
), tags AS (
    SELECT ttags.ticket_id,
           count(tags_1.name) AS tags_count,
           string_agg(tags_1.name, ','::text) AS tags
    FROM metrics.gorgias_ticket_tags ttags
             JOIN metrics.gorgias_tags tags_1 ON tags_1.id = ttags.tag_id
    GROUP BY ttags.ticket_id
)
SELECT t.id AS ticket_id,
       tm.id AS ticket_msg_id,
       t.spam,
       tm.msg_created_mountain_datetime AS msg_createdtime,
       tm.msg_sent_mountain_datetime AS msg_senttime,
       tm.from_agent,
       tm.via,
       tm.channel,
       t.created_mountain_datetime AS ticket_createdtime,
       tm.msg_sent_mountain_datetime AS min,
       t.created_mountain_datetime AS ticket_next_opening,
       CASE
           WHEN t.dow >= 1::double precision AND t.dow <= 6::double precision AND t.hour >= 7::double precision AND t.hour <= 18::double precision THEN 'yes'::text
           ELSE 'no'::text
END AS ticket_created_in_hours,
       CASE
           WHEN tm.dow >= 1::double precision AND tm.dow <= 6::double precision AND tm.hour >= 7::double precision AND tm.hour <= 18::double precision THEN 'yes'::text
           ELSE 'no'::text
END AS min_created_in_hours
FROM tickets t
         LEFT JOIN tags ON tags.ticket_id = t.id
         LEFT JOIN ticket_msgs tm ON tm.ticket_id = t.id AND tm.rank = 1;

alter table metrics.min_user_eventdate
    owner to postgres;

create or replace view metrics.shopify_orders2
            (id, order_id, shopify_id, email, status, created_at, source, current_subtotal_price_set,
             current_total_discounts_set, current_total_duties_set, current_total_price_set, current_total_tax_set,
             net_payment_set, refund_discrepancy_set, original_total_duties_set, original_total_price_set,
             subtotal_price_set, total_capturable_set, total_discounts_set, total_outstanding_set, total_price_set,
             total_received_set, total_refunded_set, total_refunded_shipping_set, total_shipping_price_set,
             total_tax_set, total_tip_received_set, meta_tag, customer_id, created_at_america_chicago)
as
SELECT id,
       order_id,
       shopify_id,
       email,
       status,
       created_at,
       source,
       current_subtotal_price_set,
       current_total_discounts_set,
       current_total_duties_set,
       current_total_price_set,
       current_total_tax_set,
       net_payment_set,
       refund_discrepancy_set,
       original_total_duties_set,
       original_total_price_set,
       subtotal_price_set,
       total_capturable_set,
       total_discounts_set,
       total_outstanding_set,
       total_price_set,
       total_received_set,
       total_refunded_set,
       total_refunded_shipping_set,
       total_shipping_price_set,
       total_tax_set,
       total_tip_received_set,
       meta_tag,
       customer_id,
       created_at_america_chicago
FROM metrics.shopify_orders
WHERE created_at_america_chicago >= '2024-09-01 00:00:00'::timestamp without time zone AND created_at_america_chicago <= now();

alter table metrics.shopify_orders2
    owner to postgres;

