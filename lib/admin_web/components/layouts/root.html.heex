<!DOCTYPE html>
<html lang="en" class="admin-web h-full">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title suffix=" · Admin Framework">
      <%= assigns[:page_title] || "Tools" %>
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="module" src={~p"/assets/app.js"}>
    </script>
    <%= if Application.get_env(:live_debugger, :browser_features?) do %>
      <script id="live-debugger-scripts" src={Application.get_env(:live_debugger, :assets_url)}>
      </script>
    <% end %>
  </head>
  <body id="body" class="bg-white dark:bg-gray-700 leading-tight min-h-screen flex flex-col">
    <div class="flex-grow">
      <%= @inner_content %>
    </div>
    <.footer />
  </body>
</html>
