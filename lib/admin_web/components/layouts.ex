defmodule AdminWeb.Layouts do
  use AdminWeb, :html
  alias AdminWeb.Policies.MenuBar

  alias Phoenix.LiveView.JS

  embed_templates "layouts/*"

  @compiled_env Application.compile_env(:admin, :environment)
  def is_dev?, do: :dev == @compiled_env

  @app_version Mix.Project.config()[:version]

  @doc """
  Renders a set of UI components to handle darkmode.
  """
  attr :theme, :string, default: "system"
  def theme_toggle(assigns) do
    ~H"""
    <div id="light-switch" phx-hook="LightSwitch" class="flex flex-col">
      <div class="block px-3 pl-0 py-1 text-xs font-medium text-gray-900 dark:text-brand-50">
        Appearance
      </div>
      <div phx-click={
            JS.dispatch("phx:set-theme",
              detail: %{
                "theme" => (@theme == "system" && "dark") || "system"
              }
            )}
            class="flex items-center cursor-pointer"
            >
        <button
          type="button"
          class={[
            (@theme == "system" && "bg-indigo-600") || "bg-gray-200",
            "relative inline-flex h-4 w-4 shrink-0 cursor-pointer rounded-md border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"
          ]}
          role="switch"
          aria-checked={@theme == "system"}
          aria-labelledby="annual-billing-label"
        >
            <Heroicons.check solid class="text-white dark:text-brand-50 h-3 w-3" :if={@theme == "system"}/>
        </button>
        <span class="ml-3 text-xs flex flex-col" id="annual-billing-label">
          <span class="font-medium text-gray-900 dark:text-brand-50">System Theme</span>
          <span class="text-gray-500 dark:text-brand-200">Overrides Preferences</span>
        </span>
      </div>

      <input type="checkbox" name="light-switch" class="light-switch sr-only" />
      <label phx-click={JS.dispatch("phx:set-theme", detail: %{"theme" => ((@theme == "dark" && "light") || "dark")})} class="relative cursor-pointer p-2" for="light-switch">
        <span class="flex dark:hidden content-center font-medium text-xs gap-3">
          <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
            <path
              class="fill-slate-300"
              d="M7 0h2v2H7zM12.88 1.637l1.414 1.415-1.415 1.413-1.413-1.414zM14 7h2v2h-2zM12.95 14.433l-1.414-1.413 1.413-1.415 1.415 1.414zM7 14h2v2H7zM2.98 14.364l-1.413-1.415 1.414-1.414 1.414 1.415zM0 7h2v2H0zM3.05 1.706 4.463 3.12 3.05 4.535 1.636 3.12z"
            />
            <path class="fill-slate-400" d="M8 4C5.8 4 4 5.8 4 8s1.8 4 4 4 4-1.8 4-4-1.8-4-4-4Z" />
          </svg>
          {@theme == "system" && "Using System" || "Set to Dark"}
        </span>
        <span class="hidden dark:flex content-center font-medium text-xs gap-3">
          <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
            <path
              class="fill-slate-400"
              d="M6.2 1C3.2 1.8 1 4.6 1 7.9 1 11.8 4.2 15 8.1 15c3.3 0 6-2.2 6.9-5.2C9.7 11.2 4.8 6.3 6.2 1Z"
            />
            <path
              class="fill-slate-500"
              d="M12.5 5a.625.625 0 0 1-.625-.625 1.252 1.252 0 0 0-1.25-1.25.625.625 0 1 1 0-1.25 1.252 1.252 0 0 0 1.25-1.25.625.625 0 1 1 1.25 0c.001.69.56 1.249 1.25 1.25a.625.625 0 1 1 0 1.25c-.69.001-1.249.56-1.25 1.25A.625.625 0 0 1 12.5 5Z"
            />
          </svg>
          {@theme == "system" && "Using System" || "Set to Light"}
        </span>
        <span class="sr-only">Switch to light / dark version</span>
      </label>
    </div>
    """
  end

  @doc """
  Renders a group for a menu.
  """
  attr :title, :string, required: true
  slot :inner_block, required: true
  slot :icon

  def menu_group(assigns) do
    ~H"""
    <li class="flex flex-col">
      <div class="text-gray-100 hover:bg-gray-600 group flex items-center px-2 pt-2 text-md font-medium rounded-md">
        {render_slot(@icon)} {@title}
      </div>
      {render_slot(@inner_block)}
    </li>
    """
  end

  @system_hotkeys [
    %{label: "Open Keyboard Shortcuts", hotkey: "?"},
    %{label: "Close Keyboard Shortcuts", hotkey: "Escape"},
  ]
  defp system_hotkeys, do: @system_hotkeys

  @doc """
  Renders a hidden keyboard shortcuts panel, and control LiveView keybinds.
  """
  attr :page_has_hotkeys?, :boolean, default: false
  def hotkeys(assigns) do
    ~H"""
      <dialog id="hotkeys" phx-hook="Hotkeys" class="fixed inset-0 bg-black/50" data-page-has-hotkeys={@page_has_hotkeys? && "true"} data-system-hotkeys={Jason.encode!(%{hotkeys: system_hotkeys()})}>
        <div class="inset-0">
          Lol. content.
        </div>
        <form method="dialog">
          <button>Eh?</button>
        </form>
      </dialog>
    """
  end

  @doc """
  Renders a group for a menu.
  """
  attr :name, :string, required: true
  attr :title, :string, required: true
  slot :inner_block, required: true
  slot :icon

  def menu_group_toggle(assigns) do
    ~H"""
    <div class="flex">
      <li
        phx-click-away={hide_menu_group(@name)}
        phx-click={show_menu_group(@name)}
        class="grow flex flex-col cursor-pointer"
      >
        <div class="text-gray-100 hover:bg-gray-600 group flex items-center p-2 text-md font-medium rounded-md">
          <span class="grow flex align-middle items-center gap-2">
            {render_slot(@icon)}
            {@title}
          </span>
        </div>
        <ul id={"#{@name}-ITEMS"} class="hidden">
          {render_slot(@inner_block)}
        </ul>
      </li>
      <li class="text-gray-100 flex flex-col content-center">
        <div class="h-[2rem] content-center">
          <Heroicons.chevron_up id={"#{@name}-CHEVRON"} class="ml-1 h-3 w-3" />
        </div>
        <div class="grow" />
      </li>
    </div>
    """
  end

  attr :name, :string, required: true
  attr :title, :string, required: true
  slot :inner_block, required: true

  def menu_item_toggle(assigns) do
    ~H"""
    <div class="flex">
      <li class="grow flex flex-col">
        <div
          phx-click-away={hide_menu_group(@name)}
          phx-click={show_menu_group(@name)}
          class="flex text-gray-300 hover:bg-gray-400 font-semibold italic text-left pl-4 rounded-md"
        >
          {@title}
        </div>
        <div id={"#{@name}-ITEMS"} class="hidden">
          <ul class="ml-4">
            <div class="border-l-2 border-gray-500">
              {render_slot(@inner_block)}
            </div>
          </ul>
        </div>
      </li>
      <li class="text-gray-100 flex flex-col content-center">
        <div class="h-[2rem] content-center">
          <Heroicons.chevron_up id={"#{@name}-CHEVRON"} class="ml-1 h-3 w-3" />
        </div>
        <div class="grow" />
      </li>
    </div>
    """
  end

  @doc """
  Renders an item in a menu.
  """
  attr :rest, :global, include: ~w(navigate patch href)
  slot :inner_block, required: true

  def menu_item(assigns) do
    ~H"""
    <li class="block">
      <a {@rest} class="block text-gray-300 hover:bg-gray-400 text-left pl-4 rounded-md">
        {render_slot(@inner_block)}
      </a>
    </li>
    """
  end

  def show_navbar(js \\ %JS{}) do
    js
    |> JS.show(to: "#menu")
    |> JS.show(
      to: "#menu_backdrop",
      transition: {
        "transition-all transform ease-in duration-150",
        "opacity-0",
        "opacity-100"
      }
    )
    |> JS.show(
      to: "#menu_items",
      transition: {
        "transition-all transform ease-in duration-150",
        "-translate-x-full opacity-0",
        "translate-x-0 opacity-100"
      }
    )
    |> JS.show(
      to: "#menu_close_button",
      transition: {
        "transition-all transform ease-out duration-150",
        "opacity-0 rotate-45 scale-75",
        "opacity-100 rotate-0 scale-100"
      }
    )
    |> JS.add_class("overflow-hidden", to: "body")
  end

  def hide_navbar(js \\ %JS{}) do
    js
    |> JS.hide(
      to: "#menu_backdrop",
      transition: {
        "transition-all transform ease-out duration-150",
        "opacity-100",
        "opacity-0"
      }
    )
    |> JS.hide(
      to: "#menu_items",
      transition: {
        "transition-all transform ease-out duration-150",
        "opacity-100 translate-x-0",
        "opacity-0 -translate-x-full"
      }
    )
    |> JS.hide(
      to: "#menu_close_button",
      time: 150,
      transition: {
        "transition-all transform ease-out duration-150",
        "opacity-100 rotate-0 scale-100",
        "opacity-0 rotate-45 scale-75"
      }
    )
    |> JS.hide(to: "#menu", time: 150, transition: {"flex", "flex", "hidden"})
    |> JS.remove_class("overflow-hidden", to: "body")
  end

  def toggle_user_menu(js \\ %JS{}) do
    transition_in =
      {"transition-all transition ease-out duration-200",
       "transform -translate-y-full opacity-0 scale-95",
       "transform translate-y-0 opacity-100 scale-100"}

    transition_out =
      {"transition-all transition ease-in duration-200",
       "transform translate-y-0 opacity-100 scale-100",
       "transform -translate-y-full opacity-0 scale-75"}

    js
    |> JS.toggle(to: "#user_menu", time: 200, in: transition_in, out: transition_out)
  end

  def show_menu_group(name, js \\ %JS{}) do
    js
    |> JS.show(to: "#" <> name <> "-ITEMS")
    |> JS.add_class("rotate-180", to: "#" <> name <> "-CHEVRON")
  end

  def hide_menu_group(name, js \\ %JS{}) do
    js
    |> JS.hide(to: "#" <> name <> "-ITEMS")
    |> JS.remove_class("rotate-180", to: "#" <> name <> "-CHEVRON")
  end

  @doc """
  Gets the current git SHA from the .git/HEAD file or environment variable.
  Returns a shortened version of the SHA or nil if not available.
  """
  def git_sha do
    cond do
      # First try to get it from environment variable (set in Docker container)
      sha = System.get_env("GIT_SHA") ->
        String.slice(sha, 0, 7)

      # Then try to read from git HEAD file (works in development)
      File.exists?(".git/HEAD") ->
        case File.read!(".git/HEAD") do
          "ref: " <> ref ->
            ref = String.trim(ref)
            if File.exists?(".git/#{ref}") do
              sha = File.read!(".git/#{ref}")
              String.slice(String.trim(sha), 0, 7)
            else
              nil
            end
          sha when byte_size(sha) >= 7 ->
            String.slice(String.trim(sha), 0, 7)
          _ ->
            nil
        end

      true -> nil
    end
  end

  @doc """
  Renders a footer with the application version.
  """
  def footer(assigns) do
    assigns =
      assigns
      |> assign_new(:app_version, fn -> @app_version end)
      |> assign_new(:git_sha, fn -> git_sha() end)

    ~H"""
    <footer class="w-full border-t-1 border-gray-200 dark:border-gray-600 mt-auto py-3 text-center text-gray-600 dark:text-gray-400 text-sm">
      <div class="container mx-auto">
        <p>
          Admin Framework v<%= @app_version %>
          <%= if Admin.Application.staging?() && @git_sha do %>
            - Ref: <%= @git_sha %>
          <% end %>
        </p>
      </div>
    </footer>
    """
  end
end
