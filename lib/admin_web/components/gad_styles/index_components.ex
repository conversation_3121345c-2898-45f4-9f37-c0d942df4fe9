defmodule AdminWeb.GadStyles.IndexComponents do
  @moduledoc """
  Provides GAD specific styling for a stylized index page. This
  layout is used on a number of different indexes. Search is optionally
  supported on the body component.

  Such as: bulk index w/ search, project pickers, user pickers, etc.

  Many of these templates require specific events to be supported on the
  page you are trying to build. Be sure to check the specific docs for
  each function/component that you use.
  """
  use AdminWeb, :component

  @doc """
  Renders a header component.

  Required Params:

  - `title`: Display a title on the header of the page
  """
  attr :title, :string, required: true
  attr :prefix, :string, required: true

  attr :search?, :boolean, default: false
  attr :search_value, :string
  attr :search_results, :any

  attr :new?, :boolean, default: false
  def page_header(assigns) do
    ~H"""
    <.header>
      <div class="flex-col sm:flex-row max-w-full overflow-x-visible flex justify-between bg-blue-50 dark:bg-brand-300/50 py-16 pl-12 pr-0 mb-4 gap-x-auto">
        <div class="flex flex-col sm:flex-row gap-x-8 w-2/3">
          <div class="mt-2 text-2xl font-bold leading-7 text-gray-900 dark:text-brand-100 sm:truncate sm:text-3xl sm:tracking-tight">
            {@title}
          </div>
          <div :if={@search?} class="grow">
            <.search_popup
              target="this"
              value={@search_value}
              event="search"
              items={@search_results}
              click_away={JS.patch("#{@prefix}/")}
              patch_prefix={"#{@prefix}"}
            />
          </div>
        </div>

        <div :if={@new?} class="inline-block">
          <.link patch={"#{@prefix}/new"} title={"New #{String.capitalize(@name)}"} class="flex items-center h-10 w-15 gap-x-1.5 rounded-md bg-blue-500 dark:bg-brand-600 px-8 py-2 mr-4 text-white dark:text-brand-50 shadow-2xs">
            <Heroicons.folder_plus outline /> New
          </.link>
        </div>
      </div>
    </.header>
    """
  end
  @doc"""
  Renders the core body of a stylized index page. This layout is responsive and has a number of features that
  can be toggled on or off, depending on what you want to support.

  Required Params:
  - `prefix`: E.g "/messaging/outbound_sms_batches", all links will be related to that prefix.
  - `name`: What is the singular term for the item we are displaying?
  - `plural`: What is the plural term for the item we are displaying?
  - `live_action`: The page `live_action` variable
  - `stream`: The stream object from Phoenix Streams
  - `selected`: The ID of the selected value

  Required Slots:
  - `modal`: The _content_ of the modal for creating / editing a record.
  - `row`: The content given to each row.
  - `show`: The content that sould be included when showing a specific record.

  Optional Slots:
  - `tabs`: The content of the tab area
  """
  attr :name, :string, required: true
  attr :prefix, :string, required: true
  attr :live_action, :any, required: true
  attr :plural, :string, required: true
  attr :stream, :any, required: true
  attr :page, :any, default: nil
  attr :items, :any, default: nil
  attr :meta, Flop.Meta, default: nil
  attr :selected, :string, required: true

  attr :new?, :boolean, default: false
  attr :edit?, :boolean, default: false

  slot :row, required: true
  slot :modal, required: true
  slot :show, required: true
  slot :tabs, required: false
  def body(%{page: page, items: items, meta: meta} = assigns) when is_nil(page) and not is_nil(meta) do
    page =
      (items || [])
      |> Admin.Paginate.to_pseudo_ash_page(meta)

    assigns =
      assigns
      |> assign(:page, page)

    body(assigns)
  end
  def body(assigns) do
    ~H"""
    <div>
      <.modal
        :if={(@live_action == :new and @new?) or
          (@live_action == :edits and @edit?)}
        id={"#{@name}_modal"}
        show
        on_cancel={JS.patch("#{@prefix}")}
      >
        {render_slot(@modal)}
      </.modal>

      <div class=" self-center m-auto w-full px-12 xs:w-full">
        <div class="">
          {render_slot(@tabs)}
          <.page_controls_offset page={@page} meta={@meta} on_turn_page="turn_page" />
        </div>
        <div
          :if={@page.count == 0}
          class="container justify-between relative sm:p-20 h-2/3 mx-auto bg-slate-50 dark:bg-slate-600 m-3 rounded-md"
        >
          <p>No #{@plural} at the time...</p>
        </div>
        <div class="flex flex-col-reverse md:flex-row m-3">
          <ul
            id={"#{@name}-stream-list"}
            phx-update="stream"
            role="list"
            class={[
              @page.count != 0 || "hidden",
              "mt-5 divide-y divide-gray-500/20 border-l-4 border-y-2 sm:border-r-0 bg-white dark:bg-slate-600 rounded-l-md border-gray-200/80 dark:border-gray-600/80 sm:mt-0",
              @live_action == :show && "w-1/3",
              @live_action != :show && "w-full"
            ]}
          >
            <li
              :for={{dom_id, item} <- @stream}
              id={dom_id}
              class={[
                "flex items-center justify-between space-x-4 overflow-hidden p-3 sm:px-6 first:rounded-tl-md last:rounded-bl-md hover:bg-brand-50/20",
                @selected == item.id && "bg-brand-200/10"
              ]}
              phx-click={
                (@selected == item.id && JS.patch("#{@prefix}/")) ||
                  JS.patch("#{@prefix}/#{item.id}")
              }
              aira-selected={@selected == item.id}
              aira-label={"Show Setup #{@name} #{item.id}"}
            >
              {render_slot(@row, item)}
            </li>
          </ul>
          <div
            :if={@live_action in [:show]}
            class="md:w-2/3 h-screen overflow-y-auto bg-slate-100 bg-opacity-50 border-r-slate-100 dark:bg-brand-200/20 rounded-r-md dark:text-brand-50"
          >
            <div class="p-4 md:p-14" id="show">
              {render_slot(@show)}
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end


  attr :item, :any, required: true
  attr :live_action, :any
  attr :prefix, :string, required: true
  attr :related_href, :string, default: nil
  attr :related_text, :string
  attr :related_target, :string
  attr :external_href, :string, default: nil
  attr :external_text, :string
  attr :external_target, :string
  attr :external_image, :string
  attr :user, :any, required: true

  slot :badge, required: false
  def row_staged(assigns) do
    # Handle nil item or user
    assigns =
      if is_nil(assigns[:item]) do
        Map.put(assigns, :item, %{name: "Unknown", id: "unknown", updated_at: NaiveDateTime.utc_now()})
      else
        assigns
      end

    assigns =
      if is_nil(assigns[:user]) do
        Map.put(assigns, :user, %{name: "Unknown", id: nil, avatar_url: "none"})
      else
        assigns
      end

    ~H"""
    <div class="w-80 truncate sm:w-80 xs:w-64">
      <div class="flex items-start gap-x-3 pb-2">
        <p class="truncate text-sm font-medium text-gray-900 dark:text-brand-50"><%= @item.name %></p>
      </div>
      <div :if={@related_href} class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-blue-500 dark:text-brand-50 hover:text-blue-300">
        <.internal_link href={@internal_href} target={@internal_target} text={@related_text} />
      </div>
      <div :if={@external_href} class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-blue-500 dark:text-brand-100 hover:text-brand-300 items-center align-center">
        <.external_link href={@external_href} target={@external_target} text={@external_text} image={@external_image} />
      </div>
    </div>

    <div :if={@badge} class="mt-1 w-24 xs:max-sm:hidden flex justify-center text-sm leading-5 text-gray-500">
      {render_slot(@badge)}
    </div>
    <div
      :if={@live_action != :show}
      class="mt-1 flex w-56 truncate xs:max-sm:hidden items-center gap-x-2 text-sm leading-5 text-gray-500 dark:text-brand-50"
    >
      <p class="truncate">Last updated <%= NaiveDateTime.to_date(Map.get(@item, :updated_at, NaiveDateTime.utc_now())) %></p>
    </div>
    <div class="items-end gap-y-8 space-y-4 ">
      <div
        :if={@live_action != :show}
        class="mt-1 w-40 truncate flex items-center justify-between gap-x-2 text-sm leading-5 text-gray-500 xs:hidden dark:text-brand-50"
      >
        <p class="truncate "><%= @user.name %></p>
        <img
          :if={!is_nil(@user) && !is_nil(@user.id) && @user.avatar_url != "none"}
          class="h-8 w-8 rounded-lg"
          src={@user.avatar_url}
          alt=""
        />
      </div>
    </div>

    <div class="relative w-4 h-12 content-center dark:text-brand-50">
      <.link
        patch={"#{@prefix}/#{@item.id}"}
        href="#"
        title="View"
        class="h-full w-full self-center"
      >
        <svg
          class="h-5 w-5 text-gray-400 dark:text-brand-50"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
          data-slot="icon"
        >
          <path
            fill-rule="evenodd"
            d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
            clip-rule="evenodd"
          />
        </svg>
      </.link>
    </div>
    """
  end
end
