<div>
  <.page_header
    name="Projects"
    title="Crm Projects"
    prefix="/crm/projects"
    search?
    search_value={@search_value}
    search_results={@results}
  />

  <.body
    name="Project"
    plural="Projects"
    prefix="/crm/projects"
    live_action={@live_action}
    meta={@meta}
    stream={@streams.projects}
    selected={@selected}
    edit?
  >
    <:tabs>
      <.tabset id="output" current_action={String.to_atom(@current_tab) || nil}>
        <:tab
          action={:all}
          patch={~p"/crm/projects/"}
        >
          All
          <.badge style="gray">
            {count()}
          </.badge>
        </:tab>
        <:tab
          action={:all}
          patch={~p"/crm/projects/parents/"}
        >
          Parents
          <.badge style="gray">
            {count(:parents)}
          </.badge>
        </:tab>
        <:tab
          action={:all}
          patch={~p"/crm/projects/children/"}
        >
          Children
          <.badge style="gray">
            {count(:children)}
          </.badge>
        </:tab>
      </.tabset>
    </:tabs>

    <:row :let={batch}>
      <.row_staged
        item={batch}
        prefix="/crm/projects/"
        live_action={@live_action}
        user={@current_user}
      />
    </:row>

    <:show>
      <.live_component
        :if={@live_action in [:show]}
        module={AdminWeb.CrmProjectsLive.Components.Show}
        id={@project.id}
        title={@page_title}
        action={@live_action}
        project={@project}
        parent={@parent}
        patch={~p"/crm/projects/"}
        current_user={@current_user}
      />
    </:show>
  </.body>
</div>
