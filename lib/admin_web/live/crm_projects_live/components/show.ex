defmodule AdminWeb.CrmProjectsLive.Components.Show do
  use AdminWeb, :live_component

  alias Crm.Projects
  alias Crm.Project

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <.header>
        Project <%= @project.id %>
        <:subtitle>Project details</:subtitle>

        <:actions>
          <.link
            patch={~p"/crm/projects/#{@project.id}/edit"}
            phx-click={JS.push_focus()}
          >
            <.button>Edit Project</.button>
          </.link>
        </:actions>
      </.header>

      <.list>
        <:item title="ID"><%= @project.id %></:item>
        <:item title="Name"><%= @project.name %></:item>
        <:item title="Description"><%= @project.description %></:item>
        <:item title="Active"><%= @project.active == "1" && "Yes" || "No" %></:item>
        <:item title="Dialer"><%= @project.dialer %></:item>
        <:item title="Max Attempts"><%= @project.max_attempts %></:item>

        <:item title="Parent Project">
          <%= if @parent do %>
            <.link navigate={~p"/crm/projects/#{@parent.id}"}>
              <%= @parent.name %>
            </.link>
          <% else %>
            None (This is a Parent project)
          <% end %>
        </:item>

        <:item title="Campaign Type"><%= @project.campaign_type %></:item>

        <:item title="Campaign IDs">
          <div class="space-y-1">
            <div>
              <span class="font-semibold">Landline:</span>
              <%= @project.landline_campaign_id || "Not set" %>
            </div>
            <div>
              <span class="font-semibold">Wireless:</span>
              <%= @project.wireless_campaign_id || "Not set" %>
            </div>
          </div>
        </:item>

        <:item title="Sensitive Information"><%= @project.sensitive_information || "None" %></:item>
        <:item title="Dial Method"><%= @project.dial_method || "Not set" %></:item>
      </.list>

      <div class="mt-6">
        <.back navigate={@patch}>Back to projects</.back>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{project: project} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)}
  end
end
