defmodule AdminWeb.CrmProjectsLive.Index do
  use AdminWeb, :live_view

  alias Crm.{Projects, Project}
  import Flop.Phoenix
  import AdminWeb.GadStyles.IndexComponents

  @impl true
  def mount(_params, _session, socket) do
    {
      :ok,
      socket
      |> assign(:page_title, "CRM Projects")
      |> assign(:container_class, "w-full p-0")
      |> put_flash(:warning, "This section is a work in progress.")
    }
  end

  @impl true
  def handle_params(params, _url, socket) do
    type =
      case socket.assigns.live_action do
        :parents -> :parents
        :children -> :children
        _ -> :all
      end

    case Projects.paginate(type, params) do
      {:ok, {projects, meta}} ->

        socket =
          socket
          |> stream(:projects, projects, reset: true)
          |> assign(:meta, meta)
          |> assign(:selected, Map.get(params, "id", ""))
          |> assign(:results, nil)
          |> assign(:search_value, "")
          |> assign(:current_tab, type |> Atom.to_string())
          |> apply_action(socket.assigns.live_action, params)

        {:noreply, socket}

      {:error, changeset} ->
        {:noreply, assign(socket, :error, changeset)}
    end
  end

  @impl true
  def handle_event("turn_page", %{"page" => page}, socket) do
    page = String.to_integer(page)
    flop = socket.assigns.flop

    # Update flop with the new page
    updated_flop = Map.put(flop, :page, page)

    # Navigate to the new page using the updated flop parameters
    params = Flop.Phoenix.build_path_params(updated_flop)

    {:noreply, push_patch(socket, to: ~p"/crm/projects?#{params}")}
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    project = Projects.get_project!(id)
    parent =
      case project.parent_id do
        pid when is_nil(pid) -> nil
        0 -> nil
        id -> Projects.get_project!(id)
      end

    socket
    |> assign(:page_title, "Project Details")
    |> assign(:project, project)
    |> assign(:parent, parent)
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "CRM Projects")
    |> assign(:project, nil)
  end

  defp apply_action(socket, :parents, _params) do
    socket
    |> assign(:page_title, "CRM Projects")
    |> assign(:project, nil)
  end

  defp apply_action(socket, :children, _params) do
    socket
    |> assign(:page_title, "CRM Projects")
    |> assign(:project, nil)
  end

  def count(type \\ :all)
  def count(:all), do: Projects.count()
  def count(:parents), do: Projects.parent_count()
  def count(:children), do: Projects.child_count()
end
