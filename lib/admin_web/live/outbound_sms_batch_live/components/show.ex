defmodule AdminWeb.OutboundSmsBatchLive.Components.Show do
  use AdminWeb, :live_component

  import AdminWeb.OutboundSmsBatchLive.Components
  import AdminWeb.GadStyles.Components.Spinner

  alias Phoenix.LiveView.AsyncResult
  alias Admin.Messaging.{OutboundSMSBatch, OutboundSMS, InboundSMS, SMSThread, OutboundSMSBatchApproval, BatchAudit}
  alias Admin.Crm.LeadFile.StagedContact

  require Ash.Query

  require Ecto.Query
  import Ecto.Query
  alias Admin.AdminRepo

  require Ecto.Query
  import Ecto.Query
  alias Admin.AdminRepo

  # MARK: Lifecycle
  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign(:lead_count, AsyncResult.loading())
      |> assign(:delivered, AsyncResult.loading())
      |> assign(:bounced, AsyncResult.loading())
      |> assign(:message_count, AsyncResult.loading())
      |> assign(:response_rate, AsyncResult.loading()) # Used for decision breakdown
      |> assign(:batch_audits, []) # For storing batch audits
      |> assign(:add, "false")
      |> assign(:active_tab, "stats") # Default active tab
      |> assign(
        delivered_count: 0,
        delivered_records: 0,
        delivered_ratio_pct: 0
      )

    {:ok, socket}
  end

  @impl true
  # Handle refresh_batch_audits update from the audit selection component
  def update(%{id: id, refresh_batch_audits: true, audit_id: audit_id}, socket) do
    # Refresh the batch data
    batch = Ash.get!(OutboundSMSBatch, id)

    # Refresh batch audits
    batch_audits = get_batch_audits(batch.id)

    socket =
      socket
      |> assign(:batch, batch)
      |> assign(:batch_audits, batch_audits)
      |> put_flash(:info, "Batch audit created successfully with ID: #{audit_id}")
      |> push_event("hide_modal", %{id: "sms-audit-modal"})

    # Restart the async tasks to refresh the data
    socket =
      socket
      |> start_async(:decision_breakdown, fn -> get_stat(batch, :decision_breakdown) end)
      |> start_async(:message_count, fn -> get_stat(batch, :message_count) end)

    {:ok, socket}
  end

  @impl true
  def update(%{id: id, current_user: current_user}, socket) do
    # Check if we're switching to a different batch
    current_batch_id = if socket.assigns[:batch], do: socket.assigns.batch.id, else: nil
    switching_batch = current_batch_id && current_batch_id != id

    with {:ok, batch} <- OutboundSMSBatch |> Ash.get(id) do
      # Fetch batch audits
      batch_audits = get_batch_audits(batch.id)


      {:ok, approvers} =
        OutboundSMSBatchApproval |> Ash.Query.filter(batch_id == ^id) |> Ash.read()

      approval_states =
        Enum.map(approvers, fn approver ->
          approver.state
        end)

      IO.inspect(approvers, label: "Approvers")

      socket =
        socket
        |> assign(batch: batch)
        |> assign(current_user: current_user)
        |> assign(batch_audits: batch_audits)
        |> assign(batch_approvers: approvers)
        |> assign(all_approved?: if(Enum.all?(approval_states, fn state -> state == :approved end), do: true, else: false) )
        |> assign(current_approver_responses: [])
        |> assign(add: "true")
        |> assign(sending_batch?: false)
        |> assign(send_progress: 0)
        |> assign(send_total: 0)
        |> assign(send_status: "")

      # Reset all async results when switching batches to ensure clean state
      socket = if switching_batch do
        socket
        |> assign(:lead_count, AsyncResult.loading())
        |> assign(:delivered, AsyncResult.loading())
        |> assign(:bounced, AsyncResult.loading())
        |> assign(:message_count, AsyncResult.loading())
        |> assign(:response_rate, AsyncResult.loading())
        |> assign(
          delivered_count: 0,
          delivered_records: 0,
          delivered_ratio_pct: 0
        )
      else
        socket
      end

      # Start async tasks to load data
      socket =
        socket
        |> start_async(:lead_count, fn -> get_stat(batch, :lead_count) end)
        |> start_async(:bounced, fn -> get_stat(batch, :bounced) end)
        |> start_async(:message_count, fn -> get_stat(batch, :message_count) end)
        |> start_async(:decision_breakdown, fn -> get_stat(batch, :decision_breakdown) end)

      {:ok, socket}
    else
      {:error, _message} ->
        socket =
          socket
          |> push_navigate(to: ~p"/messaging/outbound_text_batches/")
          |> put_flash(:warning, "Batch not found.")

        {:ok, socket}
    end
  end

  # MARK: handle_async/3
  @impl true
  def handle_async(
        :message_count,
        {:ok, %{count: count, count_i: count_i, count_o: count_o}},
        socket
      ) do
    # Create chart data
    chart_data = total_chart(count, count_o, count_i)

    # Log for debugging
    IO.puts("Sending chart-totals event with data: #{inspect(chart_data)}")

    socket =
      socket
      |> assign(:message_count, AsyncResult.ok({count, count_o, count_i}))
      |> push_event("chart-totals", chart_data)

    {:noreply, socket}
  end

  def handle_async(:lead_count, {:ok, %{count: count}}, socket) do
    batch = socket.assigns.batch

    socket =
      socket
      |> assign(:lead_count, AsyncResult.ok(count))
      |> start_async(:delivered, fn -> get_stat(batch, :delivered) end)

    {:noreply, socket}
  end

  def handle_async(:send_batch, {:ok, {:ok, total_sent}}, socket) do
    # Refresh the batch to get updated state
    batch = Ash.get!(OutboundSMSBatch, socket.assigns.batch.id)

    socket =
      socket
      |> assign(:sending_batch?, false)
      |> assign(:send_status, "Batch sent successfully! #{total_sent} messages sent.")
      |> assign(:send_progress, total_sent)
      |> assign(:batch, batch)  # Update batch with new state
      |> put_flash(:info, "SMS batch sent successfully! #{total_sent} messages sent.")

    {:noreply, socket}
  end

  def handle_async(:send_batch, {:ok, {:error, reason}}, socket) do
    socket =
      socket
      |> assign(:sending_batch?, false)
      |> assign(:send_status, "Error sending batch: #{reason}")
      |> put_flash(:error, "Failed to send SMS batch: #{reason}")

    {:noreply, socket}
  end

  def handle_async(:send_batch, {:exit, reason}, socket) do
    socket =
      socket
      |> assign(:sending_batch?, false)
      |> assign(:send_status, "Batch sending failed unexpectedly")
      |> put_flash(:error, "SMS batch sending failed: #{inspect(reason)}")

    {:noreply, socket}
  end

  def handle_async(:delivered, {:ok, %{count: count}}, socket) do
    records = socket.assigns.lead_count.result

    ratio_pct =
      if 0 == records do
        nil
      else
        count / records * 100
      end

    socket =
      socket
      |> assign(:delivered, AsyncResult.ok({count, records, ratio_pct}))
      |> assign(
        delivered_count: count,
        delivered_records: records,
        delivered_ratio_pct: ratio_pct
      )

    {:noreply, socket}
  end

  def handle_async(:decision_breakdown, {:ok, data}, socket) do
    # Create chart data
    chart_data = decision_breakdown_chart(data)

    # Log for debugging
    IO.puts("Sending chart-response_rate event with data: #{inspect(chart_data)}")

    socket =
      socket
      |> assign(:response_rate, AsyncResult.ok(data))
      |> push_event("chart-response_rate", chart_data)

    {:noreply, socket}
  end

  def handle_async(stat, {:ok, %{count: count}}, socket)
      when stat in ~w[ bounced message_count]a do
    socket =
      socket
      |> assign(stat, AsyncResult.ok(count))

    {:noreply, socket}
  end

  @impl true
  def handle_event("change-tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  @impl true
  def handle_event("show_approver_input", _, socket) do
    # Redirect to the approvals tab
    {:noreply, assign(socket, :active_tab, "approvals")}
  end

  @impl true
  def handle_event("send_batch", _, socket) do
    batch = socket.assigns.batch

    # Set loading state and start async task
    socket =
      socket
      |> assign(:sending_batch?, true)
      |> assign(:send_progress, 0)
      |> assign(:send_total, 0)
      |> assign(:send_status, "Preparing to send batch...")
      |> start_async(:send_batch, fn -> send_batch_with_progress(batch, self()) end)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:send_progress, progress, total, status}, socket) do
    socket =
      socket
      |> assign(:send_progress, progress)
      |> assign(:send_total, total)
      |> assign(:send_status, status)

    {:noreply, socket}
  end

  @impl true
  @impl Phoenix.LiveView
  def handle_event("get_approver_responses", %{"approver_num" => approver_num}, socket) do

    responses = get_approver_responses(socket.assigns.batch, approver_num)

    {:noreply, assign(socket, :current_approver_responses, responses)}
    #{:noreply, socket}
  end

  @impl true
  def get_approver_responses(batch, approver_num) do

    responses =
      InboundSMS
      |> Ash.Query.filter(batch_id ==  ^batch.id and src == ^approver_num)
      |> Ash.read!()

    #IO.inspect(responses, label: "inbound_sms")

    responses
  end

  #incomplete -unused
  def get_approver_responses_all(approver_num, socket) do
    socket.assigns.batch_approvers
    |> Enum.each(fn approver -> get_approver_responses(socket.assigns.batch, approver_num) end)

  end

  # MARK: Helpers
  def pretty_number(num) do
    Number.Delimit.number_to_delimited(num, precision: 0)
  end

  # MARK: get_stat/2

  def get_stat(%{lead_file_id: lead_file_id}, :lead_count) do
    count =
      StagedContact
      |> Ash.Query.filter(
        lead_file_id == ^lead_file_id and
          (homephone_line_type == "wireless" or
             altcompanyphone_line_type == "wireless" or
             companyphone_line_type == "wireless" or
             newphone_line_type == "wireless")
      )
      |> Ash.count!()

    %{count: count}
  end

  def get_stat(%{id: id}, :delivered) do
    count =
      OutboundSMS
      |> Ash.Query.filter(batch_id == ^id and state in [:sent])
      |> Ash.count!()

    %{count: count}
  end

  def get_stat(%{id: id}, :bounced) do
    count =
      OutboundSMS
      |> Ash.Query.filter(batch_id == ^id and state in [:error])
      |> Ash.count!()

    %{count: count}
  end

  require Ecto.Query, warn: false
  import Ecto.Query

  def get_stat(%{id: id}, :message_count) do
    count_i =
      from(
        t in SMSThread,
        join: i in InboundSMS,
        on: i.sms_thread_id == t.id,
        where: t.batch_id == ^id
      )
      |> Admin.AdminRepo.aggregate(:count, prefix: "messaging")

    count_o =
      from(
        t in SMSThread,
        join: o in OutboundSMS,
        on: o.thread_id == t.id,
        where: t.batch_id == ^id
      )
      |> Admin.AdminRepo.aggregate(:count, prefix: "messaging")

    %{count: count_i + count_o, count_i: count_i, count_o: count_o}
  end

  def get_stat(%{id: id}, :decision_breakdown) do
    # Query threads by decision for this batch
    decision_counts =
      from(t in SMSThread,
        where: t.batch_id == ^id and not is_nil(t.decision),
        group_by: t.decision,
        select: %{decision: t.decision, count: count(t.id)}
      )
      |> Admin.AdminRepo.all(prefix: "messaging")

    # Query unprocessed threads (those without a decision)
    unprocessed_count =
      from(t in SMSThread,
        where: t.batch_id == ^id and is_nil(t.decision),
        select: count(t.id)
      )
      |> Admin.AdminRepo.one(prefix: "messaging")

    # Add unprocessed to the decision counts if there are any
    decision_counts =
      if unprocessed_count && unprocessed_count > 0 do
        decision_counts ++ [%{decision: "Unprocessed", count: unprocessed_count}]
      else
        decision_counts
      end

    decision_counts
  end

  # MARK: Charts
  def total_chart(_count, count_o, count_i) do
    %{
      chart: %{
        type: "donut",
        width: 150,
        height: 150
      },
      legend: %{
        floating: true,
        offsetY: -45,
        position: "bottom"
      },
      dataLabels:
        %{
          # enabled: true
        },
      series: [count_o, count_i],
      labels: ["Outbound", "Inbound"]
    }
  end

  def responses_chart_actually_works(_who_cars) do
    %{
      series: [
        %{
          name: "Outbound",
          data: [44, 55, 41, 67, 22, 43]
        },
        %{
          name: "Inbound",
          data: [13, 23, 20, 8, 13, 27]
        }
      ],
      chart: %{
        type: "area",
        stacked: true,
        toolbar: %{show: false}
      },
      dataLabels: %{enabled: false},
      stroke: %{curve: "smooth"},
      xaxis: %{
        type: "datetime",
        # Sample from 2025-01-01
        categories: [
          "2025-01-01T00:00:00.000Z",
          "2025-01-01T01:30:00.000Z",
          "2025-01-01T02:30:00.000Z",
          "2025-01-01T03:30:00.000Z",
          "2025-01-01T04:30:00.000Z",
          "2025-01-01T05:30:00.000Z",
          "2025-01-01T06:30:00.000Z"
        ]
      },
      tooltip: %{
        x: %{format: "dd/MM/YY"}
      }
    }
  end

  def decision_breakdown_chart(decision_counts) do
    # Extract series data and labels from decision counts
    series = decision_counts |> Enum.map(fn %{count: count} -> count end)
    labels = decision_counts |> Enum.map(fn %{decision: decision} -> decision end)

    %{
      chart: %{
        type: "donut",
        width: 150,
        height: 150
      },
      legend: %{
        floating: true,
        offsetY: -45,
        position: "bottom"
      },
      dataLabels: %{
        enabled: true
      },
      series: series,
      labels: labels
    }
  end

  # Fetch batch audits for a given batch ID
  defp get_batch_audits(batch_id) do
    try do
      # Use the for_batch action to get audits for this batch
      case BatchAudit.for_batch(batch_id) do
        {:ok, audit} ->
          # If we got a single audit, wrap it in a list
          [Ash.load!(audit, [:selected_by])]
        {:error, _} ->
          # If there was an error or no audit found, return an empty list
          []
      end
    rescue
      # Handle any errors gracefully
      _ -> []
    end
  end

  # Custom batch sending function with progress updates
  defp send_batch_with_progress(batch, live_view_pid) do
    try do
      # Load the batch with approvals if they're not loaded
      batch =
        case batch.approvals do
          %Ash.NotLoaded{} -> Ash.get!(Admin.Messaging.OutboundSMSBatch, batch.id, load: [:approvals])
          _ -> batch
        end

      # Only check approvals if the batch requires them
      if batch.approvals? and not Admin.Messaging.all_approvers_signed_off?(batch) do
        {:error, "Batch requires approvals and not all approvers have approved"}
      else
        # Get or create messages
        messages =
          case Admin.Messaging.OutboundSMS.by_batch_id(batch.id) do
            {:ok, []} ->
              {:ok, messages} = Admin.Messaging.insert_batch_messages(batch)
              messages

            {:ok, messages} when is_list(messages) ->
              messages

            {:error, e} ->
              raise e

            _ ->
              {:ok, messages} = Admin.Messaging.insert_batch_messages(batch)
              messages
          end

        total_messages = length(messages)

        # Send initial progress update
        send(live_view_pid, {:send_progress, 0, total_messages, "Marking batch as executed..."})

        # Mark batch as executed
        Admin.Messaging.OutboundSMSBatch.execute(batch)

        # Send progress update
        send(live_view_pid, {:send_progress, 0, total_messages, "Sending #{total_messages} messages..."})

        # Send messages with progress updates
        messages
        |> Enum.with_index(1)
        |> Enum.each(fn {msg, index} ->
          Admin.Messaging.send_sms(msg)
          # 200ms between sends (same as original)
          Process.sleep(200)

          # Send progress update
          progress_msg = "Sent #{index}/#{total_messages} messages"
          send(live_view_pid, {:send_progress, index, total_messages, progress_msg})
        end)

        {:ok, total_messages}
      end
    rescue
      e ->
        {:error, "Failed to send batch: #{inspect(e)}"}
    end
  end
end
