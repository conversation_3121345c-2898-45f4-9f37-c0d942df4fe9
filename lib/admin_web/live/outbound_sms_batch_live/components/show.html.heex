<div class="">
  <div class="flex justify-between px-4 sm:px-0">
    <div>
      <h3 class="flex flex-col text-base font-semibold leading-7 text-gray-900">
        <span>SMS Batch Details</span>
        <.id :if={@batch} id={@batch.id} type="sms-batch" />
      </h3>
      <%!-- TODO: Fixup Link to Setup --%>
      <.link
        :if={false}
        patch={~p"/messaging/outbound_text_batches/#{@batch.id}"}
        title="Go to Batch Process"
        class="flex"
      >
        <p
          :if={@batch.state in [:queued, :scheduled, :errored]}
          class="flex max-w-2xl text-xs hover:text-sm leading-6 italic text-yellow-500 hover:underline"
        >
          Resume Drafting
        </p>
        <p
          :if={@batch.state == :sent}
          class="flex max-w-2xl text-xs hover:text-sm leading-6 italic text-yellow-500 hover:underline"
        >
          (WIP) Export Results
        </p>
        <svg
          class="h-4 w-4 m-1 hover:h-5 hover:w-5 mt-1 text-yellow-500"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          class="size-6"
        >
          <path
            fill-rule="evenodd"
            d="M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z"
            clip-rule="evenodd"
          />
        </svg>
      </.link>
    </div>
    <div class="space-x-4">
      <.link
        :if={@batch.state == :sent}
        phx-click={show_modal("sms-audit-modal")}
        title="Audit Selection">
        <.badge
          style="blue"
          link?
          title="Audit Selection"
        >
          <Heroicons.magnifying_glass_circle outline class="h-5 w-5" />
        </.badge>
      </.link>

      <.modal
        :if={@batch.state == :sent}
        id="sms-audit-modal"
        show={false}
        on_cancel={JS.hide(to: "#sms-audit-modal")}
      >
        <:title>Audit Selection</:title>
        <.live_component
          module={AdminWeb.SmsAuditSelectionLive.Select}
          id="sms-audit-selection"
          batch={@batch}
          current_user={@current_user}
        />
      </.modal>

      <.link
        :if={@batch.state not in [:cancelled, :sent]}
        patch={~p"/messaging/outbound_text_batches/#{@batch.id}/edit"}
        title="Edit Batch"
      >
        <.badge
          style="blue"
          link?
          title="Edit Setup"
        >
          <.icon name="hero-pencil" outline class="h-5 w-5" />
        </.badge>
      </.link>
      <.badge
        :if={@batch.state not in [:sent, :cancelled]}
        style="red"
        link?
        phx-click="discard"
        data-confirm={"Remove Batch: '#{@batch.name}'? This is a permanent action."}
        title="Discard Setup"
      >
        <.icon name="hero-trash"  class="h-5 w-5" />
      </.badge>
    </div>
  </div>
  <div class="flex">
    <!-- Vertical Tab Navigation -->
    <div class=" border-r border-gray-200">
      <nav class="flex flex-col space-y-1 py-4">
        <button
          phx-click="change-tab"
          phx-value-tab="overview"
          phx-target={@myself}
          class={[
            "px-3 py-2 text-sm font-medium",
            @active_tab == "overview" && "text-indigo-600 bg-indigo-50 rounded-l-md border-l-4 border-indigo-600",
            @active_tab != "overview" && "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          ]}>
          <div class="flex items-center" title="Overview">
            <Heroicons.document_text class="mr-2 h-5 w-5" />
          </div>
        </button>
        <button
          phx-click="change-tab"
          phx-value-tab="stats"
          phx-target={@myself}
          class={[
            "px-3 py-2 text-sm font-medium",
            @active_tab == "stats" && "text-indigo-600 bg-indigo-50 rounded-l-md border-l-4 border-indigo-600",
            @active_tab != "stats" && "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          ]}>
          <div class="flex items-center" title="Stats">
            <Heroicons.chart_bar class="mr-2 h-5 w-5"/>
          </div>
        </button>
        <button
          phx-click="change-tab"
          phx-value-tab="approvals"
          phx-target={@myself}
          class={[
            "px-3 py-2 text-sm font-medium",
            @active_tab == "approvals" && "text-indigo-600 bg-indigo-50 rounded-l-md border-l-4 border-indigo-600",
            @active_tab != "approvals" && "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          ]}>
          <div class="flex items-center" title="Approvals">
            <Heroicons.user_group class="mr-2 h-5 w-5"/>
          </div>
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="flex-1 px-4">
      <div id="stats-tab" class={[@active_tab != "stats" && "hidden", "w-full"]}>
        <.stat_container text="Stats">
          <.stat text="Base Leads"
            href={if @batch.lead_file_id, do: ~p"/setup/lead_files/#{@batch.lead_file_id}"}
            href_target="leadfile"
            href_text={if @batch.lead_file_id, do: "Lead File"}>
            <:icon>
              <.icon name="hero-device-phone-mobile"  class="text-white h-6 w-6" />
            </:icon>
            <.async_result :let={count} assign={@lead_count}>
              <:loading>
                <.spinner type="bars" class="pt-3" color="primary"/>
              </:loading>
              <:failed>Failed loading</:failed>
              <div>
                <span :if={count == 0} class="text-gray-400 dark:text-gray-700"> External file used. </span>
                <span :if={count != 0}> {pretty_number(count)} </span>
              </div>
            </.async_result>
          </.stat>

          <.stat
            text="Delivered"
            trend={@delivered.ok? && @delivered_records != 0 && Number.Delimit.number_to_delimited(@delivered_ratio_pct, precision: 1) <> "%"}
            trend_up?={@delivered_ratio_pct >= 90.0}
            >
            <:icon>
              <Heroicons.paper_airplane class="text-white h-6 w-6" />
            </:icon>
              <.spinner :if={@delivered.loading} type="bars" class="pt-3" color="primary"/>
              <div :if={@delivered.failed}>Failed loading</div>
              <div :if={@delivered.ok?}>{pretty_number(@delivered_count)}</div>
          </.stat>

          <.stat text="Bounced">
            <:icon>
              <Heroicons.arrow_uturn_right class="text-white h-6 w-6" />
            </:icon>
            <.async_result :let={count} assign={@bounced}>
              <:loading>
                <.spinner type="bars" class="pt-3" color="primary"/>
              </:loading>
              <:failed>Failed loading</:failed>
              {pretty_number(count)}
            </.async_result>
          </.stat>

          <.stat text="Total Messages">
            <:icon>
              <Heroicons.paper_airplane class="text-white h-6 w-6" />
            </:icon>
            <.async_result :let={{count, count_o, count_i}} assign={@message_count}>
              <:loading>
                <.spinner type="bars" class="pt-3" color="primary"/>
              </:loading>
              <:failed>Failed loading</:failed>
              <div class="flex flex-col text-xs">
                <span>Total: {pretty_number(count)}</span>
                <span>Outbound: {pretty_number(count_o)}</span>
                <span>Inbound: {pretty_number(count_i)}</span>
              </div>
            </.async_result>
          </.stat>

          <.stat chart? text="Message Counts">
            <:icon>
              <Heroicons.paper_airplane class="text-white h-6 w-6" />
            </:icon>
            <.async_result :let={{count, count_o, count_i}} assign={@message_count}>
              <:loading>
                <.spinner type="bars" class="pt-3" color="primary"/>
              </:loading>
              <:failed>Failed loading</:failed>
            </.async_result>
              <div
                id={"totals-#{@batch.id}"}
                phx-hook="ChartHook"
                phx-update="ignore"
                data-batch-id={@batch.id}
                style="width: 150px; height: 150px"
                />
          </.stat>


          <.stat chart? text="Decision Breakdown">
            <:icon>
              <Heroicons.paper_airplane class="text-white h-6 w-6" />
            </:icon>
            <.async_result assign={@response_rate}>
              <:loading>
                <.spinner type="bars" class="pt-3" color="primary"/>
              </:loading>
              <:failed>Failed loading</:failed>
            </.async_result>
              <div
                id={"response_rate-#{@batch.id}"}
                phx-hook="ChartHook"
                phx-update="ignore"
                data-batch-id={@batch.id}
                style="width: 150px; height: 150px"
                />
          </.stat>
        </.stat_container>
      </div>

      <div id="overview-tab" class={[@active_tab != "overview" && "hidden", "w-full"]}>
        <.stat_container text="Overview">
          <!-- Overview content will go here -->
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-lg font-medium text-gray-900">Batch Overview</h3>
            <p class="mt-1 text-sm text-gray-500">
              This section provides an overview of the SMS batch.
            </p>
          </div>
        </.stat_container>
      </div>

      <div id="approvals-tab" class={[@active_tab != "approvals" && "hidden", "w-full"]}>
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
          <div class="px-6 py-5 border-b border-gray-200">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Batch Approvals</h3>
            <p class="mt-1 text-sm text-gray-500">
              Manage approvers for this SMS batch
            </p>
          </div>

          <div class="px-6 py-5 grid grid-cols-1 gap-6 md:grid-cols-1">
            
            
            
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
              <!-- Known Approvers Card -->
              <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div class="px-4 py-5 sm:p-4">
                  <h3 class="text-sm font-semibold leading-6 text-gray-900">Add Known Approver</h3>
                  <div class="mt-2">
                    <form class="space-y-4" phx-submit="add_known_approver">
                      <div class="flex justify-between">
                        <div class="w-3/4">
                          <label for="known_approvers" class="block text-sm font-medium text-gray-700">Select Approver</label>
                          <select name="known_approvers" id="known_approvers" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="jj">Julie Jones</option>
                            <option value="mr">Marilea Rans</option>
                            <option value="rt">Rachel Tobak</option>
                            <option value="ps">Patrick Stallings</option>
                            <option value="em">Emily Martin</option>
                            <option value="dd">Debbie Dumke</option>
                            <option value="lm">Laura Moulton</option>
                            <option value="jw">Jim Wessel</option>
                            <option value="ec">Elizabeth Crews</option>
                          </select>
                        </div>
                        <div class="flex justify-end pt-6">
                          <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Add New Approver Card -->
              <div class="px-4 py-5 sm:p-4">
                <h3 class="text-sm font-semibold leading-6 text-gray-900">Add New Approver</h3>
                <div class="mt-2">
                  <form class="space-y-4" phx-submit="add_approver">
                    <div class="grid grid-cols-3 gap-4 sm:grid-cols-3 xs:grid-cols-2">
                      <div>
                        <label for="fname" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" name="fname" id="fname" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm xs:text-xs" placeholder="First Name">
                      </div>
                      <div>
                        <label for="lname" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" name="lname" id="lname" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm xs:text-xs" placeholder="Last Name">
                      </div>
                      <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                        <input type="text" name="phone" id="phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm xs:text-xs" placeholder="(*************">
                      </div>
                    </div>
                    <div class="flex justify-end">
                      <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Add Approver
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>


          </div>

          <!-- Approvers List -->
          <div class="px-6 py-5">
            <h3 class="text-base font-semibold leading-6 text-gray-900 mb-4">Current Approvers</h3>

            <div class="bg-white shadow overflow-hidden sm:rounded-md">
              <ul role="list" class="divide-y divide-gray-200">
                <li :for={approver <- @batch_approvers} class="px-4 py-4 sm:px-6">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                          <span class="text-indigo-800 font-medium text-sm">
                            <%= String.first(approver.first_name) %><%= String.first(approver.last_name) %>
                          </span>
                        </div>
                      </div>
                      <div class="ml-4 text-xs">
                        <div class="font-medium text-gray-900">
                          <%= approver.first_name %> <%= approver.last_name %>
                        </div>
                        <div class="text-xs text-gray-500">
                          <%= approver.phone_number %>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span :if={approver.state == :drafted} class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Draft
                      </span>
                      <span :if={approver.state == :sent} class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <span>Sent <span class="font-light"><%= DateTime.to_date(approver.sent_at)%> <%= DateTime.to_time(approver.sent_at)%></span>
                        </span>
                      </span>
                      <span :if={approver.state == :approved} class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Approved
                      </span>

                      <button :if={approver.state == :drafted} phx-click="notify_approver" phx-value-id={approver.id} class="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" title="Notify Approver">
                        <Heroicons.bell_alert mini class="h-4 w-4" />
                      </button>

                      <button :if={approver.state == :sent} phx-click="approved" phx-value-id={approver.id} 
                        class="inline-flex items-center bg-transparent rounded-full text-green-500 hover:bg-green-400/20" title="Mark as Approved">
                        <Heroicons.check_circle outline  class="h-7 w-7" />
                      </button>

                      <button :if={approver.state == :drafted} phx-click="delete_approver" phx-value-id={approver.id} class="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-red-400 bg-transparent hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" title="Delete Approver">
                        <Heroicons.trash mini class="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div :if={approver.state == :sent} class="flex mt-2 ml-14 text-sm text-blue-600">
                    <div :if={@current_approver_responses == [] and get_approver_responses(@batch, approver.phone_number) == []} class="italic"> Awaiting response... </div>
                    <div :if={@current_approver_responses != [] or get_approver_responses(@batch, approver.phone_number) != []} 
                      class="inline-flex items-center rounded-md bg-gray-400/10 px-2 py-1 text-xs font-medium text-gray-400 ring-1 ring-inset ring-gray-400/20"
                    >
                      <Heroicons.chat_bubble_left_right  class="h-4 w-4 mr-1" />
                      <div class="divide-y divide-gray-400">
                        <%= if @current_approver_responses !=[] do %>
                          <p :for={response <- @current_approver_responses}
                            class="border-l-2 border-gray-400/20 pl-2 py-1.5"
                          > 
                            "<%= response.message %>" 
                          </p>
                        <% else %>
                          <p :for={response <- get_approver_responses(@batch, approver.phone_number)}
                            class="border-l-2 border-gray-400/20 pl-2 py-1.5"
                          > 
                            "<%= response.message %>" 
                          </p>
                        <% end %> 
                      </div>
                    </div>
                    <.button phx-target={@myself} phx-click="get_approver_responses" phx-value-approver_num={approver.phone_number} class="inline-flex pb-2 items-center text-gray-400 hover:text-gray-700" title="Refresh Responses">
                      <Heroicons.arrow_path class="h-4 w-4" />
                    </.button>
                  </div>
                </li>
                <li :if={@batch_approvers == []} class="px-4 py-4 sm:px-6 text-center text-gray-500">
                  No approvers added yet
                </li>
              </ul>
            </div>
          </div>

          <!-- Eligible to Send -->
          <div class="px-6 py-5 border-t border-gray-200">
            <div class="flex items-center">
              <.button phx-click="send_batch" class="flex-shrink-0">
                <Heroicons.paper_airplane class="h-6 w-6 text-gray-500" />
                <!-- <.spinner type="pinging" />-->
              </.button>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-500" :if={@all_approved? and @batch.state != :sent}>Ready to Send</div>
                <div class="text-sm font-medium text-gray-500" :if={not @all_approved?}>Not Eligible for Send - Need All Approvals</div>
                <div class="text-sm font-medium text-gray-500" :if={@all_approved? and @batch.state == :sent}>Already Sent</div>
                <div class="text-sm font-medium text-gray-900">
                  <span>Temp - Send <%= @batch.name %></span>
                </div>
              </div>
            </div> 
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="mt-6 border-t border-gray-100">
    <dl class="flex flex-col">
      <!--
        MARK: Details List
      -->
      <div class="flex w-full flex-none gap-x-4 px-6 pt-6 justify-center">
        <dt class="flex text-sm font-medium text-gray-600">
          <Heroicons.tag class="size-5" />
          <span class="sr-only md:not-sr-only md:ml-2 asign-middle">Batch Name</span>
        </dt>
        <dd class="mt-1 text-sm text-gray-600 sm:col-span-1 sm:mt-0">
          {@batch.name}
          <.batch_state state={@batch.state} />
        </dd>
      </div>

      <div class="flex w-full flex-none gap-x-4 px-6 pt-6 justify-center">
        <dt class="flex text-sm font-medium text-gray-600">
          <Heroicons.calendar class="size-5" />
          <span class="sr-only md:not-sr-only md:ml-2 asign-middle">Date Created</span>
        </dt>
        <dd class="mt-1 text-sm text-gray-600 sm:col-span-1 sm:mt-0">
          {@batch.inserted_at}
        </dd>
      </div>

      <div class="flex w-full flex-none gap-x-4 px-6 pt-6 justify-center">
        <dt class="flex text-sm font-medium text-gray-600">
          <Heroicons.calendar_days class="size-5" />
          <span class="sr-only md:not-sr-only md:ml-2 asign-middle">Date Updated</span>
        </dt>
        <dd class="mt-1 text-sm text-gray-600 sm:col-span-1 sm:mt-0">
          {@batch.inserted_at}
        </dd>
      </div>

      <div class="flex w-full flex-none gap-x-4 px-6 pt-6 justify-center">
        <dt class="flex text-sm font-medium text-gray-600">
          <Heroicons.user_circle class="size-5" />
          <span class="sr-only md:not-sr-only md:ml-2 asign-middle">Created By</span>
        </dt>
        <dd class="flex mt-1 text-sm text-gray-600 sm:col-span-1 sm:mt-0">
          <img
            :if={
              !is_nil(@batch.user_id)
              and AdminWeb.SetupLive.Index.get_user_avatar(@batch.user_id) != "none"
              }
              class="h-5 w-5 rounded-full"
              src={"#{AdminWeb.SetupLive.Index.get_user_avatar(@batch.user_id)}"} />
            <span class="ml-2 align-middle">
              <%= AdminWeb.SetupLive.Index.get_user_name(@batch.user_id) %>
            </span>
        </dd>
      </div>

      <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900">Message Template</dt>
        <dd class="mt-1 text-sm leading-6 text-gray-600 sm:col-span-2 sm:mt-0">
          <pre class="overflow-x-scroll">
            <%= @batch.message %>
          </pre>
        </dd>
      </div>

      <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900">Batch Audits</dt>
        <dd class="mt-1 text-sm leading-6 text-gray-600 sm:col-span-2 sm:mt-0">
          <%= if Enum.empty?(@batch_audits) do %>
            <div class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-md">
              <p class="text-gray-500 mb-4">Not yet audited</p>
              <.link
                :if={@batch.state == :sent}
                phx-click={show_modal("sms-audit-modal")}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Perform Audit Selection
              </.link>
            </div>
          <% else %>
            <div class="space-y-4">
              <%= for audit <- @batch_audits do %>
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                  <div class="px-4 py-5 sm:px-6">
                    <div class="flex justify-between items-center">
                      <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                          Audit ID: <%= audit.id %>
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">
                          Created at: <%= audit.inserted_at %>
                        </p>
                      </div>
                      <.link
                        href={~p"/messaging/qc?batch_audit_id=#{audit.id}"}
                        target="_blank"
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <Heroicons.eye class="h-4 w-4 mr-1" />
                        View in QC
                      </.link>
                    </div>
                  </div>
                  <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
                    <dl class="sm:divide-y sm:divide-gray-200">
                      <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Selected by</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          <%= if audit.selected_by do %>
                            <img
                              :if={
                                !is_nil(audit.selected_by.id)
                                and AdminWeb.SetupLive.Index.get_user_avatar(audit.selected_by.id) != "none"
                                }
                                class="h-5 w-5 rounded-full"
                                src={"#{AdminWeb.SetupLive.Index.get_user_avatar(audit.selected_by.id)}"} />
                            <span class="ml-2 align-middle">
                              <%= AdminWeb.SetupLive.Index.get_user_name(audit.selected_by.id) %>
                            </span>
                          <% else %>
                            Unknown
                          <% end %>
                        </dd>
                      </div>
                      <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Selection ended at</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          <%= audit.selection_ended_at %>
                        </dd>
                      </div>
                      <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Options</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          <ul class="border border-gray-200 rounded-md divide-y divide-gray-200">
                            <%= for option <- audit.options do %>
                              <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                                <div class="w-0 flex-1 flex items-center">
                                  <span class="ml-2 flex-1 w-0 truncate">
                                    Type: <%= option["type"] %>, Percentage: <%= option["percentage"] %>%
                                  </span>
                                </div>
                              </li>
                            <% end %>
                          </ul>
                        </dd>
                      </div>
                    </dl>
                  </div>
                </div>
              <% end %>

              <.link
                :if={@batch.state == :sent}
                phx-click={show_modal("sms-audit-modal")}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Perform Another Audit Selection
              </.link>
            </div>
          <% end %>
        </dd>
      </div>


    </dl>
  </div>
</div>
