defmodule AdminWeb.Transcription.TranscribeLive.Components do
  @moduledoc """
  Provides UI functionality for the transcription live view.
  """
  use AdminWeb, :component
  alias Admin.Transcription.Field

  @doc """
  Renders the account number input.

  ## Examples

    <.account_input for={@form} />
  """
  def account_input(assigns) do
    ~H"""
    <div class="columns-2 mb-1">
      <div class="w-full text-zinc-600">
        Account Number (Numbers only)
      </div>
      <.input type="text" placeholder="Account Number (remove padding)" field={@for[:account]} />
    </div>
    """
  end

  @doc """
  Renders a date picker input.

  ## Examples

    <.date_input for={@form} />
  """
  def signed_date_input(assigns) do
    ~H"""
    <div class="columns-2 mb-1">
      <div class="w-full text-zinc-600">
        Date Signed (MM/DD/YYYY)
      </div>
      <.input type="date" field={@for[:signed_date]} />
    </div>
    """
  end

  def form_input(assigns) do
    ~H"""
    <hr :if={@field.new_block} class="py-3" />
    <div class="columns-2 mb-1">
      <div class={"w-full text-zinc-600 #{if @field.new_block, do: "font-semibold"}"}>
        #<%= @field.order_position %>, <%= @field.response_field_name %>
      </div>
      <.form_input_for
        field={@field}
        type={@field.type}
        name={input_name(@for, @field.id)}
        value={input_value(@for, @field.id)}
        label={nil}
      />
    </div>
    """
  end

  attr :type, :atom
  attr :field, Field
  attr :rest, :global, include: ~w(label name value)

  defp form_input_for(%{type: :text} = assigns) do
    ~H"""
    <.input type="text" {@rest} />
    """
  end

  defp form_input_for(%{type: :number} = assigns) do
    ~H"""
    <.input type="number" {@rest} />
    """
  end

  defp form_input_for(%{type: :checkbox} = assigns) do
    # Phoenix.HTML now prunes checkboxes with `value="false"`,
    # but at the same time now considers the `checked` field
    # to be used for the value (it used to ignore `checked`).
    # So, we have to do this.
    new_rest =
      assigns.rest
      |> Map.put(:value, "true")

    assigns =
      assigns
      |> assign(checked: Phoenix.HTML.Form.normalize_value("checkbox", assigns.rest.value))
      |> assign(rest: new_rest)

    ~H"""
    <.input type="checkbox"
      checked={@checked}
      {@rest} />
    """
  end

  defp form_input_for(%{type: :radio} = assigns) do
    ~H"""
    <div class="mx-3 inline-block">
      <ol>
        <li :for={opt <- @field.options || []}>
          <.input type="checkbox" {@rest}>
            <%= "#{opt["value"]} – #{opt["text"]}" %>
          </.input>
        </li>
      </ol>
    </div>
    """
  end

  defp form_input_for(%{type: :radio_text} = assigns) do
    ~H"""
    <div class="mx-3 inline-block flex-1">
      <.input type="text" {@rest} />
      <div class="text-xs font-sans text-zinc-500">Possable Responses</div>
      <div class="border-dashed border-2 rounded-lg border-zinc-500 p-2">
        <div :for={opt <- @field.options || []} class="text-sm text-">
          <span class="font-bold"><%= opt.value %></span>
          <span class="">-</span>
          <span class=""><%= opt.label %></span>
        </div>
      </div>
    </div>
    """
  end
end
