defmodule Admin.Messaging do
  @moduledoc """
  This module is responsible for coordinating the sending and logging sms messages.

  The main entry point for sending sms messages is Admin.Messaging.send_sms/3.

  You may send one message, or a batch of messages. Batches will spawn
  one message per recipient, and will be sent in parallel.

  Globally, there are throttle limits for sending sms messages. Messages
  spawned from a batch or send indivdually will share the same throttle limit.

  We use the AMQP protocol to enqueue batches and individual messages. The
  consumers for these queues are defined in the Admin.Messaging.*Consumer modules.

  This includes:
  - InboundSMSConsumer: Listens for inbound sms messages from all providers
  - OutboundSMSConsumer: Performs the actual sending of sms messages with throttle limits.
  - OutboundSMSBatchConsumer: Selects records to send based on the batch configuration.
    Enqueues individual messages for each recipient.
  """

  @doc """
  Sends a message to a recipient.

  ## Examples
  iex> sms =
  ..> Admin.Messaging.OutboundSMS |>
  ..> Ash.Changeset.new() |>
  ..> Ash.Changeset.for_create(:create, %{
  ..> from: "**********",
  ..> to: "**********",
  ..> message: "Hello, <PERSON>!",
  ..> requested_by: 1
  ..> }) |>
  ..> Ash.create!()
  iex> Admin.Messaging.send_sms(sms)
      {:ok, %Admin.Messaging.OutboundSMS{...}}
  iex> Admin.Messaging.send_sms(sms)
      {:error, "Message already sent."}
  """
  alias Admin.Messaging.{
    InboundSMS,
    OutboundSMS,
    OutboundSMSBatch,
    OutboundSMSBatchApproval,
    SMSThread
  }

  alias Admin.Crm.Formatter
  alias Admin.Crm.LeadFile.StagedContact
  alias Admin.Integrations.OpenAI.ChatClassifier

  require Ash.Query
  require Ecto.Query
  import Ecto.Query

  @static_sms_number "7192190654"

  @spec send_sms(message :: %OutboundSMS{}) ::
          {:ok, updated_message :: %OutboundSMS{}} | {:error, String.t()}
  def send_sms(%OutboundSMS{thread_id: nil, batch_id: batch_id} = message) do
    # Create thread, update message, and send.
    with {:ok, thread} <- SMSThread.create(:outbound, batch_id),
         {:ok, message} <-
           OutboundSMS.tie_thread(message, message.id, %{"thread_id" => thread.id}) do
      send_sms(message)
    else
      {:error, reason} -> {:error, reason}
    end
  end

  def send_sms(%OutboundSMS{thread_id: tid} = message) when not is_nil(tid) do
    # Get to have the latest copy, incase the old copy was provided
    with {:ok, message} <- Ash.get(OutboundSMS, message.id) do
      _send_sms(message)
    else
      {:error, reason} -> {:error, reason}
    end
  end

  def _send_sms(%OutboundSMS{sent_at: at}) when not is_nil(at),
    do: {:error, "Message already sent."}

  def _send_sms(%OutboundSMS{from: from, to: to, message: message} = sms) do
    # Carrier Abstraction happens here.
    # Get the configured SMS provider (real or mock)
    sms_provider = Admin.Messaging.Provider.sms_provider()

    # Send the message using the configured provider
    with :ok <- sms_provider.send_sms(from, to, message, true) do
      sms |> OutboundSMS.send()
    else
      {:error, reason} ->
        sms
        |> OutboundSMS.error(reason)

        {:error, reason}
    end
  end

  @doc """
  Adds an additional Approver to a batch.

  Takes a batch (or batch_id string), first name, last name, and phone number.

  Returns {:ok, approval} or {:error, message}

  See add_approver_well_known/2 to easily add one of the known approvers (Pat, AMs, some clients).
  """
  def add_approver(batch_id, fname, lname, phone) when is_binary(batch_id) do
    with {:ok, batch} <- Ash.get(OutboundSMSBatch, batch_id) do
      add_approver(batch, fname, lname, phone)
    else
      _ ->
        {:error, "invalid batch id"}
    end
  end

  def add_approver(%OutboundSMSBatch{} = batch, fname, lname, phone) do
    params = %{
      "batch_id" => batch.id,
      "first_name" => fname,
      "last_name" => lname,
      "phone_number" => phone,
      "message_draft" => batch.message
    }

    changeset = Ash.Changeset.for_create(OutboundSMSBatchApproval, :create, params)

    if changeset.valid? do
      Ash.create(changeset)
    else
      {:error, "invalid parameters"}
    end
  end

  def add_approver(_, _, _, _), do: {:error, "invalid batch"}

  @doc """
  A simple shortcut that adds an approver that is well known, such as Pat, a static AM, or high value clients.

  Takes a batch (or batch_id string), and an atom of a well known approver.

  Returns {:ok, approval} or {:error, message}

  see add_approver/4 to add a custom approval.
  """
  def add_approver_well_known(batch, :pat),
    do: add_approver(batch, "Patrick", "Stallings", "**********")

  def add_approver_well_known(batch, :marilea),
    do: add_approver(batch, "Marilea", "Rans", "7192135258")

  def add_approver_well_known(batch, :julie),
    do: add_approver(batch, "Julie", "Jones", "7192095300")

  def add_approver_well_known(batch, :rachel),
    do: add_approver(batch, "Rachel", "Tobak", "7194603080")

  def add_approver_well_known(batch, :jason),
    do: add_approver(batch, "Jason", "Graven", "7193044538")

  def add_approver_well_known(batch, :shirley),
    do: add_approver(batch, "Shirley", "Patino", "9155034360")

  @doc """
  Sends a batch of sms messages.
  Raises an error if the batch has could not be inserted.
  See Admin.Messaging.send_sms/1 for more information.
  """
  def insert_batch_messages!(%OutboundSMSBatch{approvals?: true, approved?: false}) do
    raise "Batch must be approved before inserting or sending."
  end

  def insert_batch_messages!(%OutboundSMSBatch{} = batch) do
    case insert_batch_messages(batch) do
      {:ok, result} ->
        result

      {:error, reason} ->
        raise reason
    end
  end

  @doc """
  Inserts messages for a batch of sms messages.

  It does this by querying for the staged contacts associated with the batch's lead file id.

  Messages will be in the queued state, and will be sent by the OutboundSMSBatchConsumer,
  provided that it hasn't been scheduled to send at a later time.

  ## Examples
  iex> batch = Ash.get!(OutboundSMSBatch, '0b1c7b7b-1b1b-4b1b-8b1b-1b1b1b1b1b1b') # Get a batch by id
  iex> Admin.Messaging.insert_batch_messages(batch)
  {:ok, [%Admin.Messaging.OutboundSMS{...}]}
  """
  def insert_batch_messages(%OutboundSMSBatch{} = batch) do
    try do
      sms_leads =
        StagedContact
        |> Ash.Query.for_read(:for_sms, %{
          lead_file_id: batch.lead_file_id
        })
        |> Ash.read!()

      from =
        case batch.from do
          from when is_binary(from) ->
            if from |> String.trim() == "" do
              @static_sms_number
            else
              from
            end

          _ ->
            @static_sms_number
        end

      {scheduled_for, state} =
        case batch.scheduled_for do
          nil ->
            {NaiveDateTime.local_now(), :queued}

          scheduled_for ->
            {scheduled_for, :scheduled}
        end

      result =
        sms_leads
        |> Enum.reject(&_blocked_wireless_state(&1.homestate))
        |> Enum.map(fn staged_contact ->
          opts = %{
            "batch_id" => batch.id,
            "from" => from,
            "to" => _first_wireless(staged_contact),
            "message" => Formatter.format(batch.message, staged_contact),
            "requested_by_id" => batch.user_id,
            "scheduled_for" => scheduled_for,
            "staged_contact_id" => staged_contact.id,
            "state" => state
          }

          Ash.create!(OutboundSMS, opts, actor: batch.user)
        end)

      {:ok, result}
    catch
      {:error, message} -> {:error, "error inserting messages: #{message}"}
      e -> {:error, e}
    end
  end

  def _first_wireless(%StagedContact{homephone_line_type: "wireless"} = staged_contact) do
    staged_contact.homephone
  end

  def _first_wireless(%StagedContact{companyphone_line_type: "wireless"} = staged_contact) do
    staged_contact.companyphone
  end

  def _first_wireless(%StagedContact{altcompanyphone_line_type: "wireless"} = staged_contact) do
    staged_contact.altcompanyphone
  end

  def _first_wireless(_), do: nil

  @doc """
  Main entry for sending a batch of sms messages.

  This function will insert the messages if they haven't been inserted yet.
  It will then send the messages in parallel, with a 200ms delay between each message.

  During the loop, each message will be refreshed and reviewed, it will silently continue
  if it already been sent.

  Should the worker die during the process, the batch will be left in the queued state.

  There is a very small chance that a failure could result in a double send of a single message.
  But this only a theoretical possibility, and has never been observed in practice.

  ## Parameters

  - `batch` - The batch to send.
  - `output_to_console` - If true, will output to the console.

  ## Examples

  iex> batch = Ash.get!(OutboundSMSBatch, '0b1c7b7b-1b1b-4b1b-8b1b-1b1b1b1b1b1b') # Get a batch by id
  iex> Admin.Messaging.send_sms_batch!(batch)
  {:ok, 10}

  iex> Admin.Messaging.send_sms_batch!(batch, true)
  Marking batch as executed now...Marked.
  Sending 10 messages.
  Start
  ..........
  Done! Batch Sent
  {:ok, 10}
  """
  def send_sms_batch!(%OutboundSMSBatch{} = batch, output_to_console \\ false) do
    maybe_log =
      if output_to_console do
        &IO.write/1
      else
        &Function.identity/1
      end

    # Load the batch with approvals if they're not loaded
    batch =
      case batch.approvals do
        %Ash.NotLoaded{} -> Ash.get!(OutboundSMSBatch, batch.id, load: [:approvals])
        _ -> batch
      end

    # Only check approvals if the batch requires them
    if batch.approvals? and not all_approvers_signed_off?(batch) do
      raise "Batch requires approvals and not all approvers have approved"
    end

    messages =
      case OutboundSMS.by_batch_id(batch.id) do
        {:ok, []} ->
          {:ok, messages} = insert_batch_messages(batch)
          messages

        {:ok, messages} when is_list(messages) ->
          messages

        {:error, e} ->
          raise e

        _ ->
          {:ok, messages} = insert_batch_messages(batch)
          messages
      end

    maybe_log.("Marking batch as executed now...")
    OutboundSMSBatch.execute(batch)
    maybe_log.("Marked.\n")

    maybe_log.("Sending #{inspect(length(messages))} messages.\nStart")

    messages
    |> Enum.map(fn msg ->
      Admin.Messaging.send_sms(msg)
      # 200ms between sends.
      Process.sleep(200)
      maybe_log.(".")
      :ok
    end)

    maybe_log.("Done! Batch Sent")

    {:ok, length(messages)}
  end

  defp all_approvers_signed_off?(%OutboundSMSBatch{approvals: approvals}) do
    Enum.all?(approvals, fn approval ->
      case approval.state do
        :approved -> true
        :sent -> false
        :drafted -> false
        :rejected -> false
        :error -> false
        _ -> false
      end
    end)
  end

  @doc """
  Notifies all approvers of a batch that it is ready for approval.
  """
  def notify_approvers(%OutboundSMSBatch{approvals: %Ash.NotLoaded{}, id: batch_id}) do
    batch = Ash.get!(OutboundSMSBatch, batch_id, load: [:approvals])

    batch
    |> notify_approvers()
  end

  def notify_approvers(%OutboundSMSBatch{approvals: approvals} = batch) do
    approvals
    |> Enum.map(fn approval ->
      batch
      |> notify_approver(approval)
    end)
  end

  @doc """
  Notifies a single approver that a batch is ready for approval.
  """
  def notify_approver(batch, %OutboundSMSBatchApproval{} = approver) do
    approval_args =
      %{
        from: @static_sms_number,
        to: approver.phone_number,
        message:
          "[The accompanying message is a sample message for approval, reply APPROVE to approve for sending, or provide comments/feedback on the message (Images/MMS not supported)]\n",
        requested_by_id: batch.user_id
      }

    approver_merge_vars = %{
      "fname" => approver.first_name,
      "lname" => approver.last_name
    }

    test_args =
      %{
        from: @static_sms_number,
        to: approver.phone_number,
        message: Admin.Crm.Formatter.format(approver.message_draft, approver_merge_vars),
        requested_by_id: batch.user_id
      }

    {:ok, approval_msg} =
      OutboundSMS
      |> Ash.Changeset.for_create(:create, approval_args)
      |> Ash.create()

    {:ok, msg} =
      OutboundSMS
      |> Ash.Changeset.for_create(:create, test_args)
      |> Ash.create()

    {:ok, _send_sms} = send_sms(approval_msg)
    # Pause for 200ms to ensure next message sends.
    Process.sleep(200)
    {:ok, send_sms} = send_sms(msg)

    # Save thread to approval
    approver
    |> OutboundSMSBatchApproval.send(approver.id, %{"thread_id" => send_sms.thread_id})
  end

  defp _blocked_wireless_state(state) do
    state in ["AZ", "LA", "MD", "NJ", "WA"]
  end

  @doc """
  Takes an inbound message, and correlates it by finding the most recent applicable thread,
  and setting the `sms_thread_id` on the message to that thread.

  Note: Somtimes we get spam into our inbound numbers.
  """
  def correlate(inbound_message) do
    with {:ok, %OutboundSMS{thread_id: t_id, batch_id: b_id} = _msg} <- msg_for(inbound_message),
         {:ok, updated_msg} <- set_refs(inbound_message, t_id, b_id) do
      {:ok, updated_msg}
    else
      {:error, "missing outbound thread"} ->
        # Might be spam?
        {:error, "unmatched inbound message"}

      _ ->
        {:error, "error saving thread id"}
    end
  end

  @doc """
  Takes an InboundSMS struct, and returns id of the latest thread via matching OutboundSMS records.

  Returns an :ok, id tupal if found, or an error tupal with a message.
  """
  def msg_for(inbound_sms) do
    ob_msg =
      from(o in OutboundSMS,
        prefix: "messaging",
        where:
          o.from == ^inbound_sms.dst and
            o.to == ^inbound_sms.src and
            o.sent_at <= ^inbound_sms.inserted_at and
            o.state == :sent,
        order_by: [desc: o.sent_at],
        limit: 1,
        select: %OutboundSMS{
          id: o.id,
          thread_id: o.thread_id,
          batch_id: o.batch_id
        }
      )
      |> Admin.AdminRepo.one()

    if is_nil(ob_msg) do
      {:error, "missing outbound thread"}
    else
      {:ok, ob_msg}
    end
  end

  @doc """
  Creates a mock inbound SMS message and publishes it to the AMQP queue.

  This function creates a message that simulates an inbound SMS from a customer,
  and publishes it to the same AMQP queue that real inbound messages would go to.
  This ensures that all the normal processing logic runs, including the LLM-based
  decision system.

  ## Parameters

  - `src` - The phone number the message is from (customer's number)
  - `dst` - The phone number the message is to (our number)
  - `message` - The content of the SMS message

  ## Returns

  - `{:ok, message_id}` - If the message was successfully published
  - `{:error, reason}` - If there was an error publishing the message

  ## Examples

  ```elixir
  Admin.Messaging.create_mock_inbound_sms("7195551234", "7192190654", "Yes I want to continue")
  ```
  """
  def create_mock_inbound_sms(src, dst, message) do
    # Generate a unique message ID
    message_id = Ecto.UUID.generate()

    # Create the payload that matches what the webhook would receive
    payload = %{
      "body" => %{
        "src" => src,
        "dst" => dst,
        "msg" => message,
        "msgid" => message_id
      }
    }

    # Convert to JSON
    case Jason.encode(payload) do
      {:ok, json_payload} ->
        # Get an AMQP channel
        case AMQP.Application.get_channel() do
          {:ok, chan} ->
            # Publish to the same queue that the webhook consumer listens to
            exchange = "webhooks"
            queue = "wh_vitelity_inbound_sms"

            # Publish the message
            case AMQP.Basic.publish(chan, exchange, queue, json_payload) do
              :ok ->
                {:ok, message_id}
              error ->
                {:error, "Failed to publish message: #{inspect(error)}"}
            end

          {:error, reason} ->
            {:error, "Failed to get AMQP channel: #{inspect(reason)}"}
        end

      {:error, reason} ->
        {:error, "Failed to encode payload: #{inspect(reason)}"}
    end
  end

  @doc """
  Takes a thread_id, and attempts to classify the type of overall decision should be made,
  then update the thread with that decision.

  Returns an ok tupal with the updated thread.
  """
  def classify_batch(batch_id, opts \\ []) do
    reclassify? = Keyword.get(opts, :reclassify?, false)

    {:ok, threads} =
      SMSThread
      |> Ash.Query.filter(batch_id == ^batch_id and (is_nil(llm_decision) or ^reclassify?))
      |> Ash.read()

    Enum.each(threads, fn thread ->
      classify_thread(thread.id)
    end)
  end

  @doc """
  Takes a thread_id, and attempts to classify the type of overall decision should be made,
  then update the thread with that decision.

  Returns an ok tupal with the updated thread.
  """
  def classify_thread(thread_id) do
    {:ok, msgs} =
      InboundSMS
      |> Ash.Query.filter(sms_thread_id == ^thread_id)
      |> Ash.read()

    total_user_input =
      msgs
      |> Enum.sort_by(& &1.inserted_at)
      |> Enum.map(& &1.message)
      |> Enum.join(" ::: ")

    case ChatClassifier.classify_chat(total_user_input) do
      {:ok, response} ->
        %{
          "category" => cat,
          "sensitive_content" => sensitive?,
          "statically_analyzed?" => statically_analyzed?
        } = response
        # Default to 1 for statically analyzed responses
        confidence = Map.get(response, "confidence", 1)

        change_thread_decision(thread_id, cat, sensitive?, statically_analyzed?)

        :ok

      _ ->
        :ok
    end
  end

  def change_thread_decision(thread_id, decision, sensitive?, statically_analyzed?) do
    {:ok, thread} = Ash.get(SMSThread, thread_id)

    {:ok, thread} =
      thread
      |> Ash.Changeset.for_update(:update, %{
        "llm_decision" => decision,
        "sensitive?" => sensitive?,
        "statically_analyzed?" => statically_analyzed?,
        "llm_decision_at" => DateTime.utc_now()
      })
      |> Ash.update()

    {:ok, thread}
  end

  @doc """
  Takes an InboundSMS struct, and assigns a given thread_id.

  Returns the new version of the InboundSMS struct in an ok tupal, or an error tupal.
  """
  def set_refs(inbound_sms, thread_id, batch_id) do
    cs =
      inbound_sms
      |> Ash.Changeset.for_update(:update, %{"sms_thread_id" => thread_id, "batch_id" => batch_id})

    if cs.valid? do
      {:ok, new_msg} = Ash.update(cs)
      {:ok, new_msg}
    else
      {:error, "invalid thread id or other save error"}
    end
  end
end
