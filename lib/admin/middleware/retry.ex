defmodule Admin.Middleware.Retry do
  require Logger

  @behaviour Tesla.Middleware

  @impl Tesla.Middleware
  def call(env, next, _opts) do
    context = %{
      retries: 0
    }

    retry(env, next, context)
  end

  defp retry(env, next, %{retries: 6}) do
    # Give up
    Tesla.run(env, next)
  end

  defp retry(env, next, context) do
    {:ok, res} = Tesla.run(env, next)

    if should_retry?(res) do
      res |> log_and_wait(context.retries)
      context = update_in(context, [:retries], &(&1 + 1))
      retry(env, next, context)
    else
      {:ok, res}
    end
  end

  defp should_retry?(%{status: 200}), do: false
  defp should_retry?(%{status: 429}), do: true
  defp should_retry?(_), do: false

  defp log_and_wait(%{headers: headers}, times) do
    case headers |> Enum.find(fn {k, _} -> k == "retry-after" end) do
      {"retry-after", value} ->
        retry_after =
          value
          |> String.to_integer()

        Logger.info("[Gorgias API] Got rate-limited #{times} time(s), retrying in #{retry_after} seconds")

        (retry_after * 1000)
        |> :timer.sleep()
    end
  end
end
