defmodule Admin.Crm.LeadFile.ScrubMetadata do
  use Ash.Resource,
    domain: Admin.Crm.Domain,
    data_layer: :embedded

  attributes do
    attribute :original_phone_number, :string do
      description "The original phone number before any transformations"
      public? true
      allow_nil? true
    end

    attribute :origin_column, :string do
      description """
      The column name the phone number came from.
      If it came from a delimited field, it will show the opsition as a list.
      List position is zero indexed.
      Example: Office Phone[2]
      """
      public? true
      allow_nil? true
    end

    attribute :result_code, :string do
      description "From DNCScrub, the result code for the phone number"
      public? true
      allow_nil? true
    end

    attribute :result_description, :string do
      description "From DNCScrub, the description of the result code"
      public? true
      allow_nil? true
    end

    attribute :removed_by, :string do
      description "If not blank, which stage removed the phone number; Pre-Processing, DNCScrub, Filter, or Suppression"
      public? true
      allow_nil? true
    end

    attribute :callable?, :boolean do
      description "Whether or not the phone number is callable"
      public? true
      default false
    end
  end

end
