defmodule Admin.Integrations.Gorgias.CustomField do
  @moduledoc """
  Module for handling custom fields in Gorgias integration.
  """

  # Define your functions and logic here
  use Ash.Resource,
  domain: Admin.Integrations.Gorgias.Domain,
  data_layer: AshPostgres.DataLayer

  code_interface do
    define :upsert, args: [:object_type, :label, :description, :priority, :required]
  end

  postgres do
    table "gorgias_custom_fields"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  identities do
    identity :label, [:label]
  end

  attributes do
    integer_primary_key :id do
      writable? true
    end

    attribute :object_type, :string do
      public? true
      allow_nil? false
    end

    attribute :label, :string do
      public? true
    end

    attribute :description, :string do
      public? true
    end

    attribute :priority, :integer do
      public? true
    end

    attribute :required, :boolean do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :upsert do
      skip_unknown_inputs :*
      accept :*
      upsert? true
      upsert_identity :label
    end
  end
end
