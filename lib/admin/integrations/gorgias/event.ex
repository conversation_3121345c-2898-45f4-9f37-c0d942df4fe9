defmodule Admin.Integrations.Gorgias.Event do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "gorgias_events"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? false
    end

    attribute :context, :string do
      public? true

      description "An uuid4 used to group a sequence of related events together, meaning that an event triggered another event and so on."
    end

    attribute :channel, :string do
      public? true
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end

    attribute :data, :map do
      public? true
    end

    attribute :object_id, :integer do
      public? true
    end

    attribute :object_type, :string do
      public? true
    end

    @doc """
    Type of the event.
    Event types are used to discriminate between events.
    List of event types: account-created,
    account-deactivated,
    account-setting-created,
    account-setting-updated,
    account-updated,
    action-executed,
    facebook-comment-created,
    facebook-comment-deleted,
    facebook-comment-edited,
    facebook-comment-hidden,
    facebook-comment-unhidden,
    facebook-comment-liked,
    facebook-comment-unliked,
    facebook-message-created,
    facebook-message-deleted,
    facebook-message-edited,
    facebook-post-created,
    facebook-post-deleted,
    facebook-post-edited,
    facebook-review-deleted,
    integration-created,
    integration-deleted,
    integration-updated,
    integration-deactivated,
    integration-reactivated,
    integration-used,
    integration-data-item-created,
    integration-data-item-deleted,
    instagram-direct-message-created,
    macro-created,
    macro-deleted,
    macro-updated,
    macro-applied,
    macro-params-updated,
    rule-created,
    rule-priority-updated,
    rule-deleted,
    rule-executed,
    rule-updated,
    rule-deactivated,
    rule-reactivated,
    rule-suggestion-suggested,
    subscription-created,
    subscription-updated,
    tag-created,
    tag-deleted,
    tag-merged,
    tag-updated,
    tag-renamed,
    team-created,
    team-deleted,
    team-updated,
    ticket-assigned,
    ticket-chat-updated,
    ticket-closed,
    ticket-created,
    ticket-customer-updated,
    ticket-deleted,
    ticket-marked-spam,
    ticket-merged,
    ticket-read-status-updated,
    ticket-reopened,
    ticket-snoozed,
    ticket-self-unsnoozed,
    ticket-subject-updated,
    ticket-tags-added,
    ticket-tags-removed,
    ticket-team-assigned,
    ticket-team-unassigned,
    ticket-trashed,
    ticket-typing-activity-shopper-started,
    ticket-unassigned,
    ticket-unmarked-spam,
    ticket-untrashed,
    ticket-updated,
    ticket-viewed,
    ticket-event-created,
    ticket-excluded-from-csat,
    ticket-message-action-failed,
    ticket-message-chat-created,
    ticket-message-created,
    ticket-message-deleted,
    ticket-message-failed,
    ticket-message-summary-created,
    ticket-message-updated,
    ticket-message-analyzed,
    customer-created,
    customer-updated,
    customer-merged,
    customer-deleted,
    customer-external-data-updated,
    custom-field-created,
    custom-field-updated,
    custom-fields-ticket-values-updated,
    self-service-configurations-updated,
    self-service-configurations-update-started,
    user-created,
    user-invited,
    user-updated,
    user-mentioned,
    user-deleted,
    user-logged-in,
    user-logged-out,
    user-impersonated,
    user-password-changed,
    user-password-reset,
    user-2fa-changed,
    user-location-updated,
    user-status-changed,
    user-typing-status-updated,
    user-setting-created,
    user-setting-updated,
    views-count-updated,
    views-deactivated,
    view-created,
    view-deleted,
    view-updated,
    view-deactivated,
    view-section-created,
    view-section-updated,
    view-section-deleted,
    widget-created,
    widget-deleted,
    widget-updated,
    satisfaction-survey-sent,
    satisfaction-survey-updated,
    satisfaction-survey-responded.
    """
    attribute :type, :string do
      public? true
      description "The type of event that was triggered."
    end

    attribute :user_id, :integer do
      public? true
    end

    attribute :uri, :string do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :import do
      skip_unknown_inputs :*
      accept :*
      description "Import a ticket from Rest API"
      upsert? true

      upsert_fields ~w(opened_datetime created_datetime updated_datetime trashed_datetime closed_datetime snooze_datetime external_id meta spam subject status via uri)a
    end
  end
end
