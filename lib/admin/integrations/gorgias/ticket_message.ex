defmodule Admin.Integrations.Gorgias.TicketMessage do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  alias Admin.Integrations.Gorgias.Ticket

  postgres do
    table "gorgias_ticket_messages"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? false
    end

    # Attributes from https://developers.gorgias.com/reference/the-ticketmessage-object
    attribute :body_text, :string do
      public? true
    end

    attribute :channel, :string do
      public? true
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end

    attribute :external_id, :string do
      public? true
    end

    attribute :failed_datetime, :utc_datetime do
      public? true
    end

    attribute :from_agent, :boolean do
      public? true
    end

    attribute :integration_id, :integer do
      public? true
    end

    attribute :message_id, :string do
      public? true
    end

    attribute :rule_id, :integer do
      public? true
    end

    attribute :sent_datetime, :utc_datetime do
      public? true
    end

    attribute :subject, :string do
      public? true
    end

    attribute :ticket_id, :integer do
      public? true
    end

    attribute :via, :string do
      public? true
    end

    attribute :uri, :string do
      public? true
    end
  end

  relationships do
    belongs_to :ticket, Ticket do
      public? true
      source_attribute :ticket_id
      destination_attribute :id
      validate_destination_attribute? false
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :import do
      skip_unknown_inputs :*
      accept :*
      description "Import a ticket-message from Rest API"
      upsert? true
    end
  end
end
