defmodule Admin.Integrations.Gorgias.Team do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "gorgias_teams"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? false
    end

    attribute :uri, :string do
      public? true
    end
    attribute :name, :string do
      public? true
    end
    attribute :description, :string do
      public? true
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end
end
