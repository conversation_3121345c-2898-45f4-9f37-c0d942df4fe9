defmodule Admin.Integrations.Gorgias.Loaders.EventLoader do
  use Admin.Integrations.Gorgias.Loaders.GorgiasLoader,
    cursor_id: 2,
    cursor_name: "event"

  @impl Loader
  def retrieve(tenant, cursor, opts) do
    Logger.debug("Retrieving events for #{inspect(tenant.id)}")

    with {:ok, response} <- RestAPI.events(tenant, cursor, opts),
      {:ok, meta, data} <- parse_api_result(response) do
        data =
          data
          |> Enum.filter(fn event ->
            event.type not in ~w(customer-created customer-updated customer-deleted customer-merged)
          end)

      {:ok, meta, data}
    else
      err -> err
    end
  end

  @impl Loader
  def process_item(tenant, event, _opts) do
    Logger.debug("Processing event #{inspect(event.id)} for #{inspect(tenant.id)}")

    result =
      Event
      |> Changeset.for_create(:import, event, tenant: tenant)
      |> Ash.create()

    case result do
      {:ok, _event} -> :ok
      {:error, %{errors: [%{field: :ticket_id}]}} -> {:error, "Ticket not found"}
      {:error, err} -> {:error, err}
      _ -> {:error, "Unknown error"}
    end
  end
end
