defmodule Admin.Integrations.Gorgias.Loaders.GorgiasLoader do
  @moduledoc """
  This module is responsible for common gorgias loader functionality. Including parsing incoming options, cursor handling, etc.

  ## Usage
  Simply `use GorgiasLoaderWorker` in your module, and provide the following parameters:

  * `:loader` - The loader module to use. This module must implement the `Loader` behaviour.
  * `:cursor_id` - The cursor id to use. This is used to store the cursor in the database.

  ## Example
  ```elixir
  defmodule Admin.Integrations.Gorgias.Loaders.TicketLoader do
    use GorgiasLoaderWorker,
      loader: Admin.Integrations.Gorgias.Loaders.TicketLoader,
      cursor_id: 1
  end
  ```
  """

  @doc """
  The `Loader` behaviour is used to define a loader module.
  """
  require Logger
  alias Admin.Integrations.Gorgias.{Loader, State, Tenant}
  @type loader_options :: [loader: module(), cursor_id: integer(), cursor_name: binary()]
  @spec __using__(options :: loader_options()) :: Macro.t()
  defmacro __using__(opts) do
    quote location: :keep do
      require Logger
      alias Ash.Changeset

      alias Admin.Integrations.Gorgias.{Loader, RestAPI, Helpers}
      alias Admin.Integrations.Gorgias.Loaders.GorgiasLoader

      import Helpers

      @behaviour Loader
      @cursor_id unquote(Keyword.get(opts, :cursor_id) || raise("Must provide a cursor id"))
      @cursor_name unquote(Keyword.get(opts, :cursor_name) || raise("Must provide a cursor name"))

      @impl Loader
      def cursor_id(), do: @cursor_id

      @impl Loader
      def cursor_name(), do: @cursor_name


      @impl Loader
      defdelegate start(tenant, state, opts \\ []), to: GorgiasLoader

      @impl Loader
      defdelegate try_page(tenant, old_cursor, new_cursor, opts \\ []), to: GorgiasLoader

      @impl Loader
      defdelegate load(tenant, cursor, opts \\ []), to: GorgiasLoader

      ### Module defined retrieve/3
      # ??????????

      ### Module defined process_item/3
      # ??????????

      @impl Loader
      defdelegate next_cursor(metadata, opts \\ []), to: GorgiasLoader

      defoverridable Loader
    end
  end

  def start(tenant, %State{} = state, opts) do
    case tenant |> try_page(state.cursor, opts) do
      {:ok, new_cursor} ->
        Logger.debug("Loader #{__MODULE__} for #{inspect(tenant.id)}: Job finished")
        _new_state = update_state!(tenant, state, new_cursor)
        :ok

      {:continue, new_cursor} ->
        Logger.debug("Loader #{__MODULE__} for #{inspect(tenant.id)}: Job continuing")
        new_state = update_state!(tenant, state, new_cursor)

        start(tenant, new_state, opts)

      {:error, message, errors} ->
        Logger.error(
          "Loader #{__MODULE__} for #{inspect(tenant.id)}: Job failed to process items: #{message}\n#{inspect(errors)}"
        )

        {:error, message, errors}

      {:error, message} ->
        Logger.error("Loader #{__MODULE__} for #{inspect(tenant.id)}: Job failed: #{message}")
        {:error, message}
    end
  end

  def try_page(tenant, cursor, opts) do
    with {:ok, new_cursor} <- load(tenant, cursor, opts) do
      cursor
      |> continue?(new_cursor, opts)
    else
      err -> err
    end
  end

  def continue?(old_cursor, new_cursor, opts) do
    reverse = Keyword.get(opts, :reverse, false)
    stop_at = Keyword.get(opts, :stop_at, Date.utc_today() |> Date.shift(month: -6))
    cursor_key = get_cursor_key(new_cursor)

    cond do
      # We couldn't parse the cursor
      cursor_key == :error ->
        Logger.debug("HALT :: Invalid cursor: #{inspect(cursor_key)} (#{inspect(new_cursor)})")
        {:error, "Invalid cursor: #{inspect(new_cursor)}"}

      # The key is a string, only the users table (small) has a string key, run to completion
      cursor_key == :name ->
        Logger.debug("CONTINUE :: String cursor: #{inspect(cursor_key)} (#{inspect(new_cursor)})")
        {:continue, new_cursor}

      # Gorgias says we have no more items, save where we were
      is_nil(new_cursor) ->
        Logger.debug("HALT :: No more items")
        {:ok, old_cursor}

      # From here, we should have a date.
      cursor_key.__struct__ != Date ->
        Logger.debug("HALT :: Invalid cursor (Not a date): #{inspect(cursor_key.__struct__)} (#{inspect(new_cursor)})")
        {:error, "Invalid cursor: #{inspect(new_cursor)}"}

      # We've reached the stop date, save where we landed.
      !reverse && cursor_key >= stop_at ->
        Logger.debug("HALT :: Reached stop date: #{inspect(cursor_key)} >= #{inspect(stop_at)}")
        {:ok, new_cursor}
      reverse && cursor_key <= stop_at ->
        Logger.debug("HALT :: Reached stop date: #{inspect(cursor_key)} <= #{inspect(stop_at)}")
        {:ok, new_cursor}

      # If they gave us the same cursor we already had, let's stop; Gorgias is confused.
      old_cursor == new_cursor ->
        Logger.debug("HALT :: Same cursor: #{inspect(old_cursor)} == #{inspect(new_cursor)}")
        {:ok, new_cursor}

      # All safeguards have passed, let's load the next page!
      true ->
        Logger.debug("CONTINUE :: Next page: #{inspect(new_cursor)}")
        {:continue, new_cursor}
    end
  end

  def load(tenant, cursor, opts) do
    loader = Keyword.get(opts, :loader)
    with {:ok, metadata, maps} <- apply(loader, :retrieve, [tenant, cursor, opts]) do
      # Process the maps
      results =
        maps
        |> Enum.map(&apply(loader, :process_item, [tenant, &1, opts]))
        |> Enum.filter(&(&1 == :ok))

      errs = Enum.filter(results, &(&1 != :ok))

      case errs do
        [] -> {:ok, next_cursor(metadata, opts)}
        _ -> {:error, "failure to process item(s)", errs}
      end
    else
      {:error, message} ->
        Logger.error("Failed to retrieve data: #{message}")
        {:error, message}
    end
  end

  # Even when we're going in reverse, we change the sort order such that the "next" page is further in the past.
  def next_cursor(metadata, _opts), do: metadata.next_cursor

  defp update_state!(tenant, state, cursor) do
    case cursor do
      nil -> Logger.debug("Clearing cursor #{state.id} for #{inspect(tenant.id)}")
      str when is_binary(str) ->
        decoded =
          str
          |> :base64.decode()
          |> inspect()

        Logger.debug("Updating cursor #{state.id} to #{decoded} for #{inspect(tenant.id)}")
    end

    state
    |> Ash.Changeset.for_update(:update, %{cursor: cursor}, tenant: tenant)
    |> Ash.update!(tenant: tenant)
  end

  def try_parse_cursor(cursor) do
    case cursor do
      nil -> nil
      str when is_binary(str) ->
        decoded =
          str
          |> :base64.decode()

        case JSON.decode(decoded) do
          {:ok, decoded} -> decoded
          {:error, _} -> nil
        end
    end
  end

  def get_cursor_key(cursor) do
    [_dir, key, _id] = try_parse_cursor(cursor) || [nil, nil, nil]

    with {:ok, dt, _} <- DateTime.from_iso8601(key <> "Z") do
      dt |> DateTime.to_date()
    else
      _ ->
        if is_binary(key), do: :name, else: :error
    end
  end
end
