defmodule Admin.Integrations.Gorgias.Loaders.CSATLoader do
  use Admin.Integrations.Gorgias.Loaders.GorgiasLoader,
    cursor_id: 4,
    cursor_name: "csat"

  @impl Loader
  def retrieve(tenant, cursor, opts) do
    Logger.debug("Retrieving CSAT surveys for #{inspect(tenant.id)}")

    tenant
    |> RestAPI.satisfaction_surveys(cursor, opts)
    |> parse_api_result()
  end

  @impl Loader
  def process_item(tenant, survey, _opts) do
    Logger.debug("Processing CSAT survey #{inspect(survey.id)} for #{inspect(tenant.id)}")
    result =
      SatisfactionSurvey
      |> Changeset.for_create(:import, survey, tenant: tenant)
      |> Ash.create(tenant: tenant)

    case result do
      {:ok, _survey} -> :ok
      {:error, %{errors: [%{field: :ticket_id}]}} -> {:error, "Ticket not found"}
      {:error, err} -> {:error, err}
    end
  end
end
