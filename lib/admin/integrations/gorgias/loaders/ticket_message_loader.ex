defmodule Admin.Integrations.Gorgias.Loaders.TicketMessageLoader do
  use Admin.Integrations.Gorgias.Loaders.GorgiasLoader,
    cursor_id: 5,
    cursor_name: "ticket_message"

  alias Admin.Integrations.Gorgias.TicketMessage

  @impl Loader
  def retrieve(tenant, cursor, opts) do
    Logger.debug("Retrieving ticket messages for #{inspect(tenant.id)}")

    tenant
    |> RestAPI.ticket_messages(cursor, opts)
    |> parse_api_result()
  end

  @impl Loader
  def process_item(tenant, message, _opts) do
    Logger.debug("Processing ticket message #{inspect(message.id)} for #{inspect(tenant.id)}")

    result =
      TicketMessage
      |> Changeset.for_create(:import, message, tenant: tenant)
      |> Ash.create(tenant: tenant)

    case result do
      {:ok, _message} -> :ok
      {:error, %{errors: [%{field: :ticket_id}]}} -> {:error, "Ticket not found"}
      {:error, err} -> {:error, err}
      _ -> {:error, "Unknown error"}
    end
  end
end
