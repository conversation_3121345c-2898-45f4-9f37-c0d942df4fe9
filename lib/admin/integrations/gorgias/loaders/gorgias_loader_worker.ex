defmodule Admin.Integrations.Gorgias.Loaders.GorgiasLoaderWorker do
  @moduledoc """
  This module is responsible for loading data from Gorgias into the Admin database.
  It supports multiple loaders and can be customized with a custor id.

  ## Usage
  Simply `use GorgiasLoaderWorker` in your module, and provide the following parameters:

  * `:loader` - The loader module to use. This module must implement the `Loader` behaviour.
  * `:cursor_id` - The cursor id to use. This is used to store the cursor in the database.

  ## Example
  ```elixir
  defmodule Admin.Integrations.Gorgias.Loaders.TicketLoader do
    use GorgiasLoaderWorker,
      loader: Admin.Integrations.Gorgias.Loaders.TicketLoader,
      cursor_id: 1
  end
  ```
  """

  @doc """
  The `Loader` behaviour is used to define a loader module.
  """
  require Logger
  alias Admin.Integrations.Gorgias.{Loader, State, Tenant}
  @type loader_options :: [loader: module(), cursor_id: integer(), cursor_name: binary()]
  @spec __using__(options :: loader_options()) :: Macro.t()
  defmacro __using__(opts) do
    quote location: :keep do
      use Oban.Worker,
        unique: [period: :infinity, states: [:available, :scheduled, :executing]]

      @cursor_id unquote(Keyword.get(opts, :cursor_id))
      @loader unquote(Keyword.get(opts, :loader))
      @cursor_name unquote(Keyword.get(opts, :cursor_name, "cursor"))

      alias Ash.Changeset
      alias Admin.Integrations.Gorgias.{State, Tenant}
      alias unquote(Keyword.get(opts, :loader)), as: Loader
      alias Admin.Integrations.Gorgias.Loaders.GorgiasLoaderWorker

      def cursor_id(), do: @cursor_id
      def cursor_name(), do: @cursor_name

      # MARK: Inherited Functions
      @impl Oban.Worker
      def perform(%Oban.Job{} = job) do
        @loader
        |> GorgiasLoaderWorker.perform(@cursor_id, job)
      end

      # MARK: Callbacks
      def enqueue_reverse_update(tenant, stop_at \\ nil) do
        @loader
        |> GorgiasLoaderWorker.enqueue_reverse_update(tenant, stop_at)
      end

      def enqueue_refresh(tenant \\ nil) do
        @loader
        |> GorgiasLoaderWorker.enqueue_refresh(tenant)
      end

      def enqueue_sync(tenant \\ nil) do
        @loader
        |> GorgiasLoaderWorker.enqueue_sync(tenant)
      end
    end
  end

  # MARK: Callbacks

  @doc """
  Called by Oban, runs the loader module.

  Job args:
  * `tenant_id` - The tenant id to load. If not provided, all tenants will be loaded in parallel.
  * `reverse` - If true, the loader will run in reverse order from now, backwards.
  * `stop_at` - Default is 3 months ago, the loader will stop at the given string date, only applicable when `reverse` is true.
  """
  @callback perform(job :: Oban.Job.t()) :: :ok

  @doc """
  Enqueues a job to run the loader in reverse order from now, backwards.

  Parameters:
  * `tenant` - The tenant struct or id to load.
  * `stop_at` - The date to stop at in YYYY-MM-DD format. If not provided, it will stop at 3 months ago.
  """
  @callback enqueue_reverse_update(tenant :: Tenant.t() | tenant_id :: integer() | nil, stop_at :: Date.t()) :: {:ok, job :: Oban.Job.t()}

  @doc """
  Fetches the current cursor, sets it to nil, and starts a loader job.

  Ideally, this should not be run directly, but rather from a module that `use`s this module.

  Parameters:
  * `tenant` - The tenant struct or id to refresh.
  """
  @callback enqueue_refresh(tenant :: Tenant.t() | tenant_id :: integer() | nil) :: :ok

  @doc """
  Starts a loader job.

  Note: Does not reset the cursor.

  Parameters:
  * `tenant` - The tenant struct or id to load.
  """
  @callback enqueue_sync(tenant :: Tenant.t() | tenant_id :: integer() | nil) :: :ok

  # MARK: Implementation


  # MARK: Perform
  @doc """
  Performs the loader for a given job, cursor id, and loader module.

  This should not be used directly, but rather through a Worker module that `use`s this module.

  For that reason, it does not `@impl Oban.Worker`.
  """
  @spec perform(loader :: module(), cursor_id :: integer(), job :: Oban.Job.t()) :: :ok
  def perform(loader, cursor_id, %Oban.Job{} = job) do
    opts = parse_opts!(loader, job.args)

    tenants =
      case job.args do
        %{"tenant_id" => tenant_id} ->
          ret = tenant(tenant_id)
          Logger.debug("Starting #{inspect(loader)} for #{inspect(ret)}")

          [ret]
        _ ->
          Logger.debug("Starting #{inspect(loader)} for all tenants")
          tenants()
      end

    tasks =
      tenants
      |> Enum.map(&to_task(&1, loader, cursor_id, opts))

    Task.await_many(tasks, :infinity)

    :ok
  end

  @spec to_task(tenant :: Tenant.t(), loader :: module(), cursor_id :: integer(), opts :: keyword()) :: Task.t()
  def to_task(%Tenant{} = tenant, loader, cursor_id, opts) do
    :ok = ensure_cursor_exists(tenant, cursor_id, cursor_name(loader))
    state = Ash.get!(State, cursor_id, tenant: tenant)

    Task.async(fn -> _perform(loader, tenant, state, opts) end)
  end

  # Performs the loader for a single tenant in a separate process.
  defp _perform(loader, tenant, state, opts) do
    Logger.debug("Starting _perform with #{inspect(loader)} for #{inspect(tenant.id)}")
    case apply(loader, :start, [tenant, state, opts]) do
      :ok -> :ok

      {:error, message, errors} ->
        Logger.error("Error loading data for loader #{inspect(loader)}: #{message}\n#{inspect(errors)}")
        {:error, "failure to process record(s): #{message}"}

      {:error, message} ->
        Logger.error("Error loading data for loader #{inspect(loader)}: #{message}")
        {:error, message}
    end
  rescue
    error ->
      Logger.error("Error loading data for loader #{inspect(loader)}: #{inspect(error)}\n#{Exception.format(:error, error, __STACKTRACE__)}")
      {:error, Exception.message(error)}
  end

  # MARK: Helpers

  @doc """
  Enqueues a job to run the loader in reverse order from now, backwards.

  Ideally, this should not be run directly, but rather from a module that `use`s this module.
  """
  @spec enqueue_reverse_update(loader :: module(), tenant :: Tenant.t() | tenant_id :: integer()) :: {:ok, job :: Oban.Job.t()}
  @spec enqueue_reverse_update(loader :: module(), tenant :: Tenant.t() | tenant_id :: integer(), stop_at :: Date.t()) :: {:ok, job :: Oban.Job.t()}
  def enqueue_reverse_update(loader, tenant, stop_at \\ nil)
  def enqueue_reverse_update(loader, tenant_id, stop_at) when is_integer(tenant_id) do
    tenant = tenant(tenant_id)
    enqueue_reverse_update(loader, tenant, stop_at)
  end
  def enqueue_reverse_update(loader, %Tenant{} = tenant, stop_at) do
    stop_at =
      case stop_at do
        %Date{} = stop_at -> stop_at
        at when is_binary(at) -> at
        _ -> Date.utc_today() |> Date.shift(month: -3)
      end

    _state =
      Ash.get!(State, cursor_id(loader), tenant: tenant)
      |> Ash.Changeset.for_update(:update, %{cursor: nil}, tenant: tenant)
      |> Ash.update!(tenant: tenant)

    args = %{"tenant_id" => tenant.id, "reverse" => true, "stop_at" => stop_at}

    loader
    |> worker_for()
    |> apply(:new, [args])
    |> Admin.Oban.insert()
  end

  @doc """
  Fetches the current cursor, sets it to nil, and starts a loader job.

  Ideally, this should not be run directly, but rather from a module that `use`s this module.
  """
  @spec enqueue_refresh(loader_module :: module(), tenant :: Tenant.t() | tenant_id :: integer()) :: {:ok, job :: Oban.Job.t()}
  def enqueue_refresh(loader, tenant)

  def enqueue_refresh(loader, tenant_id) when is_integer(tenant_id) do
    tenant = tenant(tenant_id)
    enqueue_refresh(loader, tenant)
  end

  def enqueue_refresh(loader, %Tenant{} = tenant) do
    :ok = ensure_cursor_exists(tenant, cursor_id(loader), cursor_name(loader))
    Logger.debug("Refreshing #{inspect(loader)} for #{inspect(tenant.id)}")
    _state =
      Ash.get!(State, cursor_id(loader), tenant: tenant)
      |> Ash.Changeset.for_update(:update, %{cursor: nil}, tenant: tenant)
      |> Ash.update!(tenant: tenant)

    args = %{"tenant_id" => tenant.id}

    loader
    |> worker_for()
    |> apply(:new, [args])
    |> Admin.Oban.insert()
  end

  @doc """
  Starts a loader job.

  Note: Does not reset the cursor.
  """
  @spec enqueue_sync(loader_module :: module(), tenant :: Tenant.t() | tenant_id :: integer()) :: {:ok, job :: Oban.Job.t()}
  def enqueue_sync(loader, tenant)
  def enqueue_sync(loader, tenant_id) when is_integer(tenant_id) do
    tenant = tenant(tenant_id)
    enqueue_sync(loader, tenant)
  end
  def enqueue_sync(loader, %Tenant{} = tenant) do
    :ok = ensure_cursor_exists(tenant, cursor_id(loader), cursor_name(loader))

    args = %{"tenant_id" => tenant.id}

    loader
    |> worker_for()
    |> apply(:new, [args])
    |> Admin.Oban.insert()
  end

  @doc """
  Ensures a cursor exists for the given tenant and cursor id.
  """
  def ensure_cursor_exists(tenant, cursor_id, name) do
    case Ash.get(State, cursor_id, tenant: tenant) do
      {:error, _msg} ->
        Logger.debug("Creating cursor #{cursor_id} for #{name}")
        State
        |> Ash.Changeset.for_create(:create, %{id: cursor_id, name: name})
        |> Ash.create!(tenant: tenant)

        :ok
      _ ->
        Logger.debug("Cursor #{cursor_id} for #{name} already exists")
        :ok
    end
  end

  # MARK: Internal Helpers
  defp tenants, do: Ash.read!(Tenant)
  defp tenant(id), do: Ash.get!(Tenant, id)

  defp worker_for(loader) do
    name = loader |> Atom.to_string()

    name <> "Worker"
    |> String.to_existing_atom()
  end

  defp cursor_id(loader) do
    loader
    |> worker_for()
    |> apply(:cursor_id, [])
  end

  defp cursor_name(loader) do
    loader
    |> worker_for()
    |> apply(:cursor_name, [])
  end

  defp parse_opts!(loader, args) do
    args
    |> Enum.map(fn
      {"stop_at", v} -> {:stop_at, v |> Date.from_iso8601!()}
      {k, v} -> {String.to_existing_atom(k), v}
    end)
    |> Keyword.new()
    |> Keyword.put(:loader, loader)
  rescue
    error ->
      Logger.error("Error parsing job args: #{inspect(error)}\n#{Exception.format_stacktrace(error)}")
      reraise error, __STACKTRACE__
  end
end
