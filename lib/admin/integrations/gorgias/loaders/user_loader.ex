defmodule Admin.Integrations.Gorgias.Loaders.UserLoader do
  use Admin.Integrations.Gorgias.Loaders.GorgiasLoader,
    cursor_id: 3,
    cursor_name: "user"

  alias Admin.Integrations.Gorgias.{Loader, User}

  @impl Loader
  def retrieve(tenant, cursor, opts) do
    Logger.debug("Retrieving users for #{inspect(tenant.id)}")

    tenant
    |> RestAPI.users(cursor, opts)
    |> parse_api_result()
  end

  @impl Loader
  def process_item(tenant, user, _opts) do
    Logger.debug("Processing user #{inspect(user.id)} for #{inspect(tenant.id)}")

    result =
      User
      |> Changeset.for_create(:import, user, tenant: tenant)
      |> Ash.create()

    case result do
      {:ok, _user} -> :ok
      {:error, err} -> {:error, err}
      _ -> {:error, "Unknown error"}
    end
  end
end
