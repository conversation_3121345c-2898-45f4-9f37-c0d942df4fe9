defmodule Admin.Integrations.Gorgias.Loaders.CustomerLookupWorker do
  use Oban.Worker,
    unique: [period: :infinity, states: [:available, :scheduled, :executing]],
    max_attempts: 1

  alias Admin.Integrations.Gorgias.Loaders.TicketLoader
  alias Admin.Integrations.Gorgias.Tenant

  @impl Oban.Worker
  def perform(job) do
    tenant_id = job.args["tenant_id"]
    tenant =
      Tenant
      |> Ash.get!(tenant_id)

    # Run the lookup script, NOT recursively
    # This will take a few minutes, but not more than 5 minutes (default Oban timeout)
    # Measured tests show that it takes about 3 minutes to run
    TicketLoader.lookup_customers(tenant, limit: 200)

    :ok
  end
end
