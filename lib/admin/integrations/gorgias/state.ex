defmodule Admin.Integrations.Gorgias.State do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  alias Admin.Integrations.Gorgias.Domain

  code_interface do
    define :upsert
  end

  postgres do
    table "gorgias_state"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  identities do
    identity :id, [:name]
  end

  attributes do
    integer_primary_key :id do
      writable? true
    end

    attribute :name, :string do
      public? true
      allow_nil? false
    end

    attribute :cursor, :string do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :upsert do
      skip_unknown_inputs :*
      accept :*
      upsert? true
      upsert_identity :name
    end
  end
end
