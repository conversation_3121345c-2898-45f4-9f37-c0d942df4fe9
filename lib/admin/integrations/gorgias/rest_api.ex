defmodule Admin.Integrations.Gorgias.RestAPI do
  require Logger
  alias Admin.Integrations.Gorgias
  use Tesla

  plug Tesla.Middleware.FormUrlencoded
  plug Tesla.Middleware.JSON, engine_opts: [keys: :atoms]
  # This is absurd, there are a huge number of customers that have been
  # merged with another customer, which was then merged into another, and another.
  plug Tesla.Middleware.FollowRedirects, max_redirects: 150

  plug Admin.Middleware.Retry

  def client(%Gorgias.Tenant{} = tenant) do
    Logger.debug("Creating client for #{inspect(tenant.id)}")
    Tesla.client([
      {Tesla.Middleware.BaseUrl, "https://#{tenant.subdomain}.gorgias.com/api/"},
      {Tesla.Middleware.Headers, [
        {"Authorization", "Basic #{tenant.authorization_token}"}
      ]}
    ])
  end

  def customer(tenant, id) do
    Logger.debug("Fetching customer #{id} for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/customers/#{id}")
  end

  def ticket(tenant, id) do
    Logger.debug("Fetching ticket #{id} for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/tickets/#{id}")
  end

  def tickets(tenant, opts) do
    Logger.debug("Fetching tickets for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/tickets?#{params_for(nil, opts)}")
  end

  def tickets(tenant, cursor, opts) do
    Logger.debug("Fetching tickets for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/tickets?#{params_for(cursor, opts)}")
  end

  def ticket_messages(tenant, opts) do
    Logger.debug("Fetching ticket messages for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/messages?#{params_for(nil, opts)}")
  end

  def ticket_messages(tenant, cursor, opts) do
    Logger.debug("Fetching ticket messages for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/messages?#{params_for(cursor, opts)}")
  end

  def events_for(tenant, ticket_id, "") when is_integer(ticket_id) do
    Logger.debug("Fetching events for ticket #{ticket_id} for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/events?limit=100&object_type=Ticket&object_id=#{ticket_id}")
  end

  def events_for(tenant, ticket_id, cursor) when is_integer(ticket_id) and is_binary(cursor) do
    Logger.debug("Fetching events for ticket #{ticket_id} for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/events?limit=100&object_type=Ticket&object_id=#{ticket_id}&cursor=#{cursor}")
  end

  def events(tenant, opts) do
    Logger.debug("Fetching events for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/events?limit=100&#{params_for(nil, opts)}")
  end

  def events(tenant, cursor, opts) do
    Logger.debug("Fetching events for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/events?limit=100&#{params_for(cursor, opts)}")
  end

  def users(tenant, opts) do
    Logger.debug("Fetching users for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/users?#{params_for(nil, opts)}")
  end

  def users(tenant, cursor, opts) do
    Logger.debug("Fetching users for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/users?#{params_for(cursor, opts)}")
  end

  def satisfaction_surveys(tenant, opts) do
    Logger.debug("Fetching satisfaction surveys for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/satisfaction-surveys?limit=100&#{params_for(nil, opts)}")
  end

  def satisfaction_surveys(tenant, cursor, opts) do
    Logger.debug("Fetching satisfaction surveys for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/satisfaction-surveys?limit=100&#{params_for(cursor, opts)}")
  end

  def custom_field(tenant, id) do
    Logger.debug("Fetching custom field #{id} for #{inspect(tenant.id)}")
    client(tenant)
    |> get("/custom-fields/#{id}")
  end

  def params_for(cursor, opts) do
    dir = Keyword.get(opts, :reverse, false)
    "#{direction_for(dir)}&#{cursor_for(cursor)}"
  end
  def direction_for(true = reverse), do: "order_by=created_datetime:desc"
  def direction_for(false = reverse), do: "order_by=created_datetime:asc"
  def cursor_for(cursor) when is_nil(cursor), do: ""
  def cursor_for(cursor) when is_binary(cursor), do: "cursor=#{cursor}"
end
