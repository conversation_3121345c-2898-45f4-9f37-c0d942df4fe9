defmodule Admin.Integrations.Gorgias.Loader do
  alias Admin.Integrations.Gorgias
  @type cursor :: String.t() | nil
  @type loader_opts ::
    {:reverse, boolean()} |
    {:stop_at, String.t()} |
    {:tenant_id, integer()} |
    {:loader, module()}

  @type loader_errors ::
    {:error, message :: String.t()} |
    {:error, message :: String.t(), errors :: [{:error, message :: String.t()}]}

  @doc """
  Starts the loader process, must provide tenant.

  See try_page/4 for more information.
  """
  @callback start(
    tenant :: Gorgias.Tenant.t(),
    state :: Gorgais.State.t(),
    opts :: [loader_opts]
    ) :: :ok | loader_errors()

  @doc """
  Returns the cursor id for the loader.
  """
  @callback cursor_id() :: integer()

  @doc """
  Returns the cursor name for the loader.
  """
  @callback cursor_name() :: String.t()

  @type loader_result ::
    {:ok, new_cursor :: cursor()} |
    loader_errors()

  @doc """
  Loads a page of data, from the old cursor to the new cursor.

  This is the raw loader function, and does not handle any edge cases.
  """
  @callback load(tenant :: Gorgias.Tenant.t(), cursor :: cursor() | nil, opts :: keyword()) :: loader_result()

  @type continue_result ::
    {:ok, new_cursor :: cursor()} |
    {:continue, new_cursor :: cursor()}

  @doc """
  Determines if the loader should continue, based on the old and new cursors, and the options.

  This handles a number of edge cases, such as:

  - Maintains the optional direction
  - Stops if the next page (directionally) is nil
  - Stops if the next page (directionally) beyond the stop date
  - Stops if the next page (directionally) is the same as the new cursor (Gorgias bug)

  Returns `{:ok, new_state}` if the loader finished successfully.
  Returns `{:continue, new_cursor}` if the loader needs to continue.
  """
  @callback continue?(old_cursor :: cursor(), new_cursor :: cursor(), opts :: keyword()) :: continue_result()


  @type try_page_result ::
    loader_result() |
    continue_result()

  @doc """
  Tries to load a page of data, from the a cursor.

  Upon success, uses continue?/3 to determine if the loader should continue.

  Upon failure:
  Returns `{:error, message, errors}` if the loader failed to process items.
  Returns `{:error, message}` if the loader failed to start or failed durring the run.
  """
  @callback try_page(
    tenant :: Gorgias.Tenant.t(),
    old_cursor :: cursor(),
    new_cursor :: cursor(),
    opts :: keyword()
    ) :: try_page_result()

  @doc """
  Retrives a page of data, defined with a cursor.

  Each module will define their own retrieve/3 function.
  """
  @callback retrieve(tenant :: Gorgias.Tenant.t(), cursor :: cursor() | nil, opts :: keyword()) :: {:ok, metadata :: map(), items :: [map()]} | {:error, binary}

  @doc """
  Processes a single item from the retrieve/3 function.

  Each module will define their own process_item/3 function.
  """
  @callback process_item(tenant :: Gorgias.Tenant.t(), item :: map(), opts :: keyword()) :: :ok | {:error, binary}

  @doc """
  Provides the next cursor, taking directional options into consideration.
  """
  @callback next_cursor(metadata :: map(), opts :: keyword()) :: cursor()

end
