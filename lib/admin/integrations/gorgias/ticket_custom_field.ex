defmodule Admin.Integrations.Gorgias.TicketCustomField do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  alias Admin.Integrations.Gorgias.{Ticket, CustomField}

  code_interface do
    define :upsert, args: [:ticket_id, :custom_field_id, :value]
  end

  postgres do
    table "gorgias_ticket_custom_fields"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  identities do
    identity :ticket_customfield, [:ticket_id, :custom_field_id]
  end

  attributes do
    integer_primary_key :id

    attribute :value, :string do
      public? true
    end

    attribute :ticket_id, :integer do
      public? true
    end

    attribute :custom_field_id, :integer do
      public? true
    end
  end

  relationships do
    belongs_to :ticket, Ticket do
      public? true
      validate_destination_attribute? false
    end

    belongs_to :custom_field, CustomField do
      public? true
      validate_destination_attribute? false
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :upsert do
      #skip_unknown_inputs :*
      accept :*
      upsert? true
      upsert_identity :ticket_customfield
    end
  end
end
