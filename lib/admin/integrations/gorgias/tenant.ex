defmodule Admin.Integrations.Gorgias.Tenant do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "gorgias_tenants"
    schema "metrics"
    repo Admin.AdminRepo
    manage_tenant do
      template ["gorgias_", :schema_name]
    end
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? true
    end

    attribute :name, :string do
      public? true
    end

    attribute :authorization_token, :string do
      public? false
      sensitive? true
    end

    attribute :subdomain, :string do
      public? true
    end

    attribute :schema_name, :string do
      description "The name of the schema for this tenant"
      public? :true
      allow_nil? false
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end

    attribute :updated_datetime, :utc_datetime do
      public? true
    end

    attribute :active, :boolean do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  alias Ash.Resource.Info
  defimpl Ash.ToTenant do
    def to_tenant(%{id: id, schema_name: schema_name}, resource) do
      data_layer = Info.data_layer(resource)
      strategy = Info.multitenancy_strategy(resource)
      if data_layer == AshPostgres.DataLayer && strategy == :context do
        "gorgias_#{schema_name}"
      else
        id
      end
    end
  end
end
