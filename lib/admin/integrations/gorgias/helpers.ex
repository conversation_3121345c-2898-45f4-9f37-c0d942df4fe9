defmodule Admin.Integrations.Gorgias.Helpers do
require <PERSON><PERSON>

  def parse_api_result({:ok, result}), do: parse_api_result(result)

  def parse_api_result(%{status: 200, body: body}) do
    Logger.debug("API Response: #{inspect(length(body.data || []))} records.")
    {:ok, body.meta, body.data}
  end

  def parse_api_result(%{status: status, body: body}) do
    {:error, "Unexpected status code: #{status}", body}
  end

  def parse_api_result({:error, error}) do
    {:error, "Unexpected error", error}
  end
end
