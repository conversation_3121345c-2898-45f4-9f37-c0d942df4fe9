defmodule Admin.Integrations.Gorgias.SatisfactionSurvey do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "gorgias_csat_surveys"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  identities do
    identity :ticket_id, [:ticket_id]
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? false
    end

    attribute :body_text, :string do
      public? true
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end

    attribute :customer_id, :integer do
      public? true
    end

    attribute :meta, :map do
      public? true
    end

    attribute :score, :integer do
      public? true
    end

    attribute :scored_datetime, :utc_datetime do
      public? true
    end

    attribute :sent_datetime, :utc_datetime do
      public? true
    end

    attribute :should_send_datetime, :utc_datetime do
      public? true
    end

    attribute :ticket_id, :integer do
      public? true
    end

    attribute :uri, :string do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :import do
      skip_unknown_inputs :*
      accept :*
      description "Import a user from Rest API"
      upsert? true
      upsert_identity :ticket_id
    end
  end
end
