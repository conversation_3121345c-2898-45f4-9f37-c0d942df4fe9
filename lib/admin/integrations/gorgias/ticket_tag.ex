defmodule Admin.Integrations.Gorgias.TicketTag do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  alias Admin.Integrations.Gorgias.{Ticket, Tag}

  postgres do
    table "gorgias_ticket_tags"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  attributes do
    integer_primary_key :id
  end

  relationships do
    belongs_to :ticket, Ticket do
      public? true
      validate_destination_attribute? false
    end

    belongs_to :tag, Tag do
      public? true
      validate_destination_attribute? false
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end
end
