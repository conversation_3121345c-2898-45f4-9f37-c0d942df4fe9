defmodule Admin.Integrations.Gorgias.Domain do
  use Ash.Domain,
    extensions: [AshAdmin.Domain]


  alias Admin.Integrations.Gorgias.{
    Tenant,
    User,
    State,
    Ticket,
    Team,
    SatisfactionSurvey,
    Event,
    Tag,
    TicketTag,
    TicketMessage,
    CustomField,
    TicketCustomField
  }

  admin do
    show?(true)
  end

  resources do
    resource Tenant
    resource User
    resource TicketTag
    resource State
    resource Ticket
    resource TicketMessage
    resource Team
    resource SatisfactionSurvey
    resource Event
    resource Tag
    resource CustomField
    resource TicketCustomField
  end
end
