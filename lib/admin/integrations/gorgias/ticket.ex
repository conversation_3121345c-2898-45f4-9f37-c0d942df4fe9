defmodule Admin.Integrations.Gorgias.Ticket do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  alias Admin.Integrations.Gorgias.{Event, TicketTag, Tag}

  postgres do
    table "gorgias_tickets"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? false
    end

    attribute :channel, :string do
      public? true
    end

    attribute :opened_datetime, :utc_datetime do
      public? true
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end

    attribute :updated_datetime, :utc_datetime do
      public? true
    end

    attribute :trashed_datetime, :utc_datetime do
      public? true
    end

    attribute :closed_datetime, :utc_datetime do
      public? true
    end

    attribute :snooze_datetime, :utc_datetime do
      public? true
    end

    attribute :external_id, :string do
      public? true
    end

    attribute :meta, :map do
      public? true
    end

    attribute :spam, :boolean do
      public? true
    end

    attribute :subject, :string do
      public? true
    end

    attribute :status, :string do
      public? true
    end

    attribute :via, :string do
      public? true
    end

    attribute :uri, :string do
      public? true
    end

    attribute :gorgias_customer_id, :integer do
      public? true
    end

    attribute :shopify_order_id, :integer do
      public? true
    end

    attribute :shopify_order_name, :string do
      public? true
    end

    attribute :shopify_customer_id, :integer do
      public? true
    end

    attribute :shopify_customer_id_looked_up, :boolean do
      public? true
      default false
    end

    attribute :satisfaction_survey, :map do
      public? true
    end

    attribute :assignee_team, :string do
      public? true
    end
  end

  relationships do
    many_to_many :tags, Tag do
      public? true
      through TicketTag

      source_attribute :id
      source_attribute_on_join_resource :ticket_id

      destination_attribute_on_join_resource :tag_id
      destination_attribute :id
    end

    has_many :events, Event do
      public? true
      source_attribute :id
      destination_attribute :object_id
    end
  end

  actions do
    defaults [:read, :destroy, create: :*]

    update :update do
      skip_unknown_inputs :*
      accept :*
      require_atomic? false
    end

    create :import do
      skip_unknown_inputs :*
      accept :*
      argument :tags, {:array, :integer}

      description "Import a ticket from Rest API"
      upsert? true

      upsert_fields [
        :opened_datetime,
        :created_datetime,
        :updated_datetime,
        :trashed_datetime,
        :closed_datetime,
        :snooze_datetime,
        :external_id,
        :meta,
        :spam,
        :subject,
        :status,
        :via,
        :uri,
        :gorgias_customer_id,
        :shopify_customer_id,
        :shopify_customer_id_looked_up
      ]

      change manage_relationship(:tags, type: :append_and_remove)
    end
  end
end
