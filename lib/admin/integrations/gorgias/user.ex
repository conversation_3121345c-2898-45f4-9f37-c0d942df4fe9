defmodule Admin.Integrations.Gorgias.User do
  use Ash.Resource,
    domain: Admin.Integrations.Gorgias.Domain,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "gorgias_users"
    repo Admin.AdminRepo
  end

  multitenancy do
    strategy :context
  end

  identities do
    identity :email, [:email]
  end

  attributes do
    integer_primary_key :id do
      writable? true
      generated? false
    end

    attribute :active, :boolean do
      public? true
    end

    attribute :bio, :string do
      public? true
    end

    attribute :created_datetime, :utc_datetime do
      public? true
    end

    attribute :deactivated_datetime, :utc_datetime do
      public? true
    end

    attribute :country, :string do
      public? true
    end

    attribute :email, :string do
      public? true
    end

    attribute :external_id, :string do
      public? true
    end

    attribute :firstname, :string do
      public? true
    end

    attribute :lastname, :string do
      public? true
    end

    attribute :meta, :map do
      public? true
    end

    attribute :name, :string do
      public? true
    end

    attribute :role, :map do
      public? true
    end

    attribute :timezone, :string do
      public? true
    end

    attribute :updated_datetime, :utc_datetime do
      public? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :import do
      skip_unknown_inputs :*
      accept :*
      description "Import a user from Rest API"
      upsert? true
      upsert_identity :email

      upsert_fields ~w(active bio created_datetime deactivated_datetime country email external_id firstname lastname meta name role timezone updated_datetime)a
    end
  end
end
