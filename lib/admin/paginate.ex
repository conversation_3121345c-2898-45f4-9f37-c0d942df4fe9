defmodule Admin.Paginate do
  @moduledoc """
  This macro extends any Ecto.Schema to include shortcuts for pagination via Flop.

  ## Examples

  Enable pagination, with all fields sortable and filterable.
  ```elixir
  defmodule Admin.Project do
    use Ecto.Schema
    use Admin.Paginate,
      repo: Crm.Repo

  end
  ```

  Enable pagination, but specify which fields can be used to filter and/or sort.

  ```elixir
  defmodule Admin.Project do
    use Ecto.Schema
    use Admin.Paginate,
      repo: Crm.Repo,
      filterable: [:name, :description],
      sortable: [:name, :description]

  end
  ```
  """
  defmacro __using__(opts) do
    repo =
      case Keyword.fetch(opts, :repo) do
        {:ok, repo} -> repo
        :error -> raise "Must provide a repo to Admin.Paginate"
      end


    filterable_fields =
      case Keyword.fetch(opts, :filterable) do
        {:ok, filterable} -> quote do
          unquote(filterable)
        end
        :error -> quote do
          raise "Must provide filterable fields to Admin.Paginate"
        end
      end

    sortable_fields =
      case Keyword.fetch(opts, :sortable) do
        {:ok, sortable} -> quote do
          unquote(sortable)
        end
        :error -> quote do
          raise "Must provide sortable fields to Admin.Paginate"
        end
      end

    quote do
      def all_schema_fields, do: @all_schema_fields
      def filterable_fields, do: unquote(filterable_fields)
      def sortable_fields, do: unquote(sortable_fields)

      @doc """
      Gets a page of results for the given query and Flop struct.

      Raises if the Flop/meta is invalid.
      """
      def paginate!(query \\ __MODULE__, flop \\ %Flop{})
      def paginate!(query, flop) do
        query
        |> Flop.validate!(flop)
        |> Flop.run(flop, repo: unquote(repo))
      end

      @doc """
      Gets a page of results for the given query and Flop struct.
      """
      def paginate(query \\ __MODULE__, flop \\ %Flop{})
      def paginate(query, flop) do
        with {:ok, flop} <- Flop.validate(flop) do
          ret =
            query
            |> Flop.run(flop, repo: unquote(repo))
          {:ok, ret}
        else
          {:error, error} -> {:error, error}
        end
      end

      @derive {
        Flop.Schema,
        filterable: unquote(filterable_fields),
        sortable: unquote(sortable_fields)
      }
    end
  end

  def to_pseudo_ash_page(results, %Flop.Meta{} = meta) do
    %Ash.Page.Offset{
      results: results,
      limit: meta.page_size,
      offset: meta.current_offset,
      count: meta.total_count,
      more?: meta.has_next_page?
    }
  end
end
