defmodule Admin.Transcription.Mailer.Jobs.Declaimer do
  use Admin.Jobs.RecursiveJob
  import Ecto.Query, warn: false

  alias Admin.Jobs.RecursiveJobOptions
  alias Admin.Transcription.Mailer
  alias Admin.AdminRepo, as: Repo

  @impl true
  def fetch_batch(%RecursiveJobOptions{limit: limit, schedule_in: cut_off_seconds}) do
    # Don't de-claim all mailers, only de-claim mailers that were claimed before
    # the last job started.
    # Otherwise, the de-claim job would potentually de-claim a mailer that was
    # claimed the previous second.
    from(m in Mailer,
      where: m.status == :claimed and m.updated_at < ago(^cut_off_seconds, "second"),
      select: m.id,
      order_by: [desc: m.id],
      limit: ^limit
    )
    |> Repo.all()
  end

  @impl true
  def perform_batch(_job, _job_options, batch) do
    batch
    |> Enum.map(fn id ->
      Phoenix.PubSub.broadcast(Admin.PubSub, "mailers:#{id}", {:active?, id, self()})
    end)

    {:ok, messages} = consume_messages_for(:timer.seconds(5))

    declaimable =
      batch
      |> Enum.reject(fn id ->
        Enum.member?(messages, {id, true})
      end)

    declaimable
    |> Enum.map(fn id ->
      id
      |> Mailer.get_by_id!()
      |> Mailer.declaim()
    end)

    :ok
  end

  defp consume_messages_for(total_milliseconds) do
    consume_messages_for([], total_milliseconds)
  end

  defp consume_messages_for(messages, 0), do: {:ok, messages}

  defp consume_messages_for(messages, remaining_milliseconds) do
    time_start = System.os_time(:millisecond)

    receive do
      message ->
        diff = System.os_time(:millisecond) - time_start
        consume_messages_for([message | messages], (remaining_milliseconds - diff) |> max(0))
    after
      remaining_milliseconds ->
        {:ok, messages}
    end
  end
end
