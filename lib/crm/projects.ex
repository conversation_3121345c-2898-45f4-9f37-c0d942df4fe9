defmodule Crm.Projects do
  @moduledoc """
  The Projects context.

  It uses the MySQL backed Ecto Schema `Crm.Project`.

  Calls to list projects are cached into an ETS table internally on OTP.
  """
  use GenServer

  import Ecto.Query, warn: false

  alias Crm.{Project, EffortProjects}
  alias Crm.Repo
  alias Dialer.ViciDial

  @table :crm_project_cache

  @name __MODULE__

  def start_link(opts) do
    GenServer.start_link(__MODULE__, opts, name: @name)
  end

  @impl true
  def init(stack) do
    :ets.new(@table, [:named_table, :public, :ordered_set])

    {:ok, stack}
  end

  @doc """
  Returns the list of projects.

  ## Examples

      iex> list_projects()
      [%Project{}, ...]

  """
  def list_projects do
    retrieve_cache_item(:all)
  rescue
    _ ->
      query =
        from p in Project,
          select: p,
          where: p.active == "1",
          order_by: [desc: p.id]

      Repo.all(query) |> store_cache_item(:all)
  end

  def head_parents do
    from(p in Project,
      where:
        p.active == "1" and
          like(p.name, "Parent%") and
          p.parent_id == 0,
      order_by: [desc: p.id],
      limit: 20
    )
    |> Repo.all()
  end

  def recent_projects_count do
    retrieve_cache_item(:recent_projects_count)
  rescue
    _e ->
      target = Timex.shift(Timex.now(), days: -30)

      from(p in Project, where: p.parent_id == 0 and p.inserted_at > ^target, select: count())
      |> Repo.one()
      |> store_cache_item(:recent_projects_count)
  end

  def count do
    retrieve_cache_item(:count)
  rescue
    _ ->
      from(p in Project, select: count())
      |> Repo.one()
      |> store_cache_item(:count)
  end

  def parent_count do
    retrieve_cache_item(:parent_count)
  rescue
    _ ->
      from(p in parents_query(), select: count())
      |> Repo.one()
      |> store_cache_item(:parent_count)
  end

  def child_count do
    retrieve_cache_item(:child_count)
  rescue
    _ ->
      from(p in children_query(), select: count())
      |> Repo.one()
      |> store_cache_item(:child_count)
  end

  defp parents_query do
    from(p in Project,
      where: (p.parent_id == 0 or is_nil(p.parent_id)) and like(p.name, "Parent%"),
      order_by: [desc: p.id]
    )
  end
  def parents do
    query = parents_query()

    Repo.all(query)
  end

  defp children_query do
    from(p in Project,
      where: p.parent_id != 0 and not is_nil(p.parent_id) and like(p.name, "Child%"),
      order_by: [desc: p.id]
    )
  end
  def children do
    query = children_query()

    Repo.all(query)
  end

  @doc """
  Paginates the projects using Flop.

  ## Examples

      iex> paginate(:parents, %{page: 1, page_size: 20})
      {:ok, {projects, %Flop.Meta{} = meta}}

  """
  def paginate(type \\ :all, opts \\ %{})
  def paginate(:all, opts) do
    opts =
      opts
      |> Map.put_new("order_by", [:id])
      |> Map.put_new("order_directions", [:desc])
      |> Map.put_new("page_size", 20)
      |> Map.put_new("pagination_type", :page)
      |> Map.put_new("for", Project)

    query = from(p in Project, select: p, order_by: [desc: p.id])

    Project.paginate(query, opts)
  end

  def paginate(:parents, opts) do
    opts =
      opts
      |> Map.put_new("order_by", [:id])
      |> Map.put_new("order_directions", [:desc])
      |> Map.put_new("page_size", 20)
      |> Map.put_new("pagination_type", :page)
      |> Map.put_new("for", Project)

    query =
      from(p in parents_query(),
      select: p
      )

    Project.paginate(query, opts)
  end

  def paginate(:children, opts) do
    opts =
      opts
      |> Map.put_new("order_by", [:id])
      |> Map.put_new("order_directions", [:desc])
      |> Map.put_new("page_size", 20)
      |> Map.put_new("pagination_type", :page)
      |> Map.put_new("for", Project)

    query =
      from(p in children_query(),
      select: p
      )

    Project.paginate(query, opts)
  end


  def landline_parent(campaign_id) do
    query =
      from p in Project,
        select: %{value: p.id, text: p.name},
        where: p.landline_campaign_id == ^campaign_id

    Repo.one(query)
  end

  def wireless_parent(campaign_id) do
    query =
      from p in Project,
        select: %{value: p.id, text: p.name},
        where: p.wireless_campaign_id == ^campaign_id

    Repo.one(query)
  end

  def children do
    query =
      from p in Project,
        select: %{value: p.id, text: p.name},
        where: p.parent_id != 0,
        order_by: [desc: p.id]

    Repo.all(query)
  end

  def children(parent_id) do
    query =
      from p in Project,
        select: p,
        where: p.parent_id == ^parent_id,
        order_by: [asc: p.id]

    Repo.all(query)
  end

  def children2(parent_id) do
    query =
      from p in Project,
        select: %{value: p.id, text: p.name},
        where: p.parent_id == ^parent_id,
        order_by: [asc: p.id]

    Repo.all(query)
  end

  def group(gid) do
    project = get_project!(gid)

    result =
      if project.parent_id == 0 and
           project.landline_campaign_id == nil and
           project.wireless_campaign_id == nil and
           Regex.match?(~r/ G |RQG|NNG| G| G_w/, project.name) do
        isgroup = true

        isgroup
      else
        isgroup = false

        isgroup
      end

    result
  end

  def container(cid) do
    project = get_project!(cid)

    result =
      if project.landline_campaign_id == nil and
           project.wireless_campaign_id == nil and
           Regex.match?(~r/ C |RQC|NNC| C| C_w/, project.name) do
        iscontainer = true

        iscontainer
      else
        iscontainer = false

        iscontainer
      end

    result
  end

  def group_get_fpen(group_id, container_id) do
    # query = Ecto.Query.from(
    #             d in fragment("CALL GroupGetPenetratedGroupDialIds(?, ?)", ^group_id, ^container_id),
    #             #[group_id, container_id],
    #             select: d.dialid

    #           )

    {:ok, result} =
      Ecto.Adapters.SQL.query(Crm.Repo, "CALL GroupGetPenetratedGroupDialIds(?, ?)", [
        group_id,
        container_id
      ])

    # Repo.all(query)
  end

  def contactability_script(project_ids) do
    # Enum.map(project_ids, fn _ -> "?" end) |> Enum.join(", ")
    placeholders = "?"

    # Convert list to comma seperated string
    projids = Enum.join(project_ids, ",")

    # projids = project_ids |> Enum.map(&String.to_integer/1)
    IO.puts("in query: #{inspect(projids)}")

    sql =
      """
      CALL Crm_Contactability('#{projids}')
      """

    # sql = """
    #   CALL Crm_Contactability(?)
    # """

    IO.puts("in query - sql: #{inspect(sql)}")

    # ,[], timeout: :infinity) do
    %{columns: columns, rows: rows, num_rows: num} =
      case Repo.query!(sql, [], timeout: :infinity) do
        # case Ecto.Adapters.SQL.query_many(Crm.Repo, sql, [projids], timeout: :infinity) do
        # #case Ecto.Adapters.SQL.query(Crm.Repo, "CALL Crm_Contactability(#{projids})") do
        {:ok, result} ->
          IO.inspect(result, label: "Suceess, contactability results: ")

        {:error, error} ->
          IO.inspect(error, label: "Error, contactability results: ")

        result ->
          # IO.inspect(result , label: "In, contactability results: ")
          result
      end

    %{c_cols: columns, c_rows: rows, num_of_rows: num}
  end

  @doc """
  Gets a single project.

  Raises if the Project does not exist.

  ## Examples

      iex> get_project!(123)
      %Project{}

  """
  def get_project!(id) do
    if id == nil, do: nil, else: Repo.get!(Project, id)
  end

  def get_project(id) do
    get_project!(id)
  rescue
    e -> {:error, "No such project: #{inspect(e)}"}
  end

  def get_dialer(id) do
    project = get_project!(id)

    if project.parent_id == 0 do
      throw("project has no parent")
    end

    parent = get_project!(project.parent_id)
    {:ok, _parse_dialer(parent)}
  rescue
    e -> {:error, "ProjectID #{id} does not exist, or: #{inspect(e)}"}
  end

  defp _parse_dialer(%{assignments: %{dialer: "Wireless"}}), do: :wireless
  defp _parse_dialer(%{landline_campaign_id: id}) when is_binary(id), do: :landline
  defp _parse_dialer(%{wireless_campaign_id: id}) when is_binary(id), do: :wireless

  defp _parse_campaign_id(%{landline_campaign_id: id}) when is_binary(id), do: id
  defp _parse_campaign_id(%{wireless_campaign_id: id}) when is_binary(id), do: id

  def add_child(parent_id, name) do
    # FIXME: Some campaigns may have been copied with NBSPs in the name
    parent = get_project!(parent_id)

    max = if parent.dialer == "Landline", do: 10, else: 4

    params = %{
      name: "CHILD " <> name,
      description: parent.name,
      active: "1",
      parent_id: parent.id,
      dialer: parent.dialer,
      max_attempts: max
    }

    {:ok, child} = create_project(params)

    dialer_repo = _repo_for(parent.dialer)
    {:ok, _} = ViciDial.add_list(dialer_repo, _parse_campaign_id(parent), child.id, child.name)

    {:ok, child}
  end

  def convert_to_group_calling(id) do
    convert_to_group_calling!(id)
  rescue
    e -> {:error, "Failed to convert project to group calling: #{inspect(e)}"}
  end

  def convert_to_group_calling!(id) do
    project = get_project!(id)
    parent = get_project!(project.parent_id)

    if project.campaign_type == "Group" do
      raise "Already a group calling project"
    end

    base_name = project.name

    project =
      Project.changeset(project, %{
        name: _replace_campaign_type_suffix(base_name, "S"),
        campaign_type: "Group"
      })
      |> Repo.update!()

    container =
      Project.changeset(%Project{}, %{
        name: _replace_campaign_type_suffix(base_name, "C"),
        campaign_type: "Group",
        dialer: parent.dialer,
        parent_id: project.parent_id,
        max_attempts: parent.max_attempts
        # inserted_at: project.inserted_at,
        # updated_at: project.updated_at
      })
      |> Repo.insert!()

    group =
      Project.changeset(%Project{}, %{
        name: _replace_campaign_type_suffix(base_name, "G"),
        campaign_type: "Group",
        dialer: parent.dialer,
        parent_id: 0,
        max_attempts: parent.max_attempts
        # inserted_at: project.inserted_at,
        # updated_at: project.updated_at
      })
      |> Repo.insert!()

    Dialer.insert_list(
      group.id,
      group.name,
      parent |> Project.campaign_id(),
      project |> Project.dialer()
    )

    {:ok, container: container.id, group: group.id}
  end

  def _make_group_calling!(project) do
    project
    |> Project.changeset(%{
      name: _replace_campaign_type_suffix(project.name, "S"),
      campaign_type: "Group"
    })
    |> Repo.update!()
  end

  def _replace_campaign_type_suffix(name, suffix) do
    Regex.replace(~r/(RQ|NN|NQ)/i, name, "\\1 #{suffix}")
  end

  @doc """
  Creates a Parent Project and clones the Campaign from the base campaign.
  """
  def create_parent(dialer, name, campaign_id, effort_id, base_campaign_id) do
    {:ok, parent} = create_parent(dialer, name, campaign_id, effort_id)
    dialer_repo = _repo_for(dialer)

    {:ok, _} =
      ViciDial.clone_campaign(dialer_repo, base_campaign_id, campaign_id, name, parent.id)

    {:ok, parent}
  end

  @doc """
  Creates a Parent Project, this version expects the campaign to already exist on the dialer.

  If the Campaign cloning is skipped, there will be reporting errors.

  See `create_parent/4` for a version that clones the campaign.
  """
  def create_parent(dialer, name, campaign_id, effort_id) do
    max = if dialer == "landline", do: 10, else: 4

    params = %{
      name: name,
      description: name,
      active: "1",
      dialer: dialer,
      max_attempts: max,
      campaign_type: "Parent",
      landline_campaign_id: (dialer == "landline" && campaign_id) || nil,
      wireless_campaign_id: (dialer == "wireless" && campaign_id) || nil
    }

    {:ok, parent} = create_project(params)

    {:ok, _ep} =
      Crm.EffortProjects.create_effort_project(%{
        effort_id: effort_id,
        project_id: parent.id
      })

    {:ok, parent}
  end

  @doc """
  Creates a project.

  ## Examples

      iex> create_project(%{field: value})
      {:ok, %Project{}}

      iex> create_project(%{field: bad_value})
      {:error, ...}

  """
  def create_project(attrs \\ %{}) do
    %Project{}
    |> Project.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a project.

  ## Examples

      iex> update_project(project, %{field: new_value})
      {:ok, %Project{}}

      iex> update_project(project, %{field: bad_value})
      {:error, ...}

  """
  def update_project(%Project{} = project, attrs) do
    project
    |> Project.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a Project.

  ## Examples

      iex> delete_project(project)
      {:ok, %Project{}}

      iex> delete_project(project)
      {:error, ...}

  """
  def delete_project(%Project{} = project) do
    Repo.delete(project)
  end

  @doc """
  Returns a data structure for tracking project changes.

  ## Examples

      iex> change_project(project)
      %Todo{...}

  """
  def change_project(%Project{} = project, attrs \\ %{}) do
    Project.changeset(project, attrs)
  end


  @doc """
  Deletes a project and the related effortproject

  """
  def _project_cleanup(project_id) do
    query =
      from p in Crm.Project,
        where: p.id == ^project_id

    Repo.delete_all(query)



    #delete_project(project_id)


  end

  def _parent_update(parents) do

    parents
    |> Enum.map(
      fn parent ->
        case is_nil(parent) do
          true ->
            nil
          false ->
            p = get_project(parent)

            field = if p.dialer == "Landline", do: "landline_campaign_id", else: "wireless_campaign_id"
            result = String.to_atom(field)

            append = "Parent Trash"

            p_query =
              from(
                p in Crm.Project,
                update: [set: [{^result, "CM2"}, name: fragment("CONCAT(?, ' ' , ?)", ^append, p.name)]],
                where: p.id == ^parent
              )

            Repo.update_all(p_query,[])

            EffortProjects.delete_effort_project(parent)
        end

      end
    )

  end

  defp store_cache_item(item, key) do
    :ets.insert(@table, {key, item, Timex.now()})
    item
  end

  defp retrieve_cache_item(key) do
    [{_key, item, time}] = :ets.lookup(@table, key)

    if Timex.before?(Timex.shift(Timex.now(), minutes: -5), time) do
      item
    else
      :ets.delete(@table, key)
      item
    end
  end

  defp _repo_for("Landline"), do: Dialer.Landline.Repo
  defp _repo_for("landline"), do: Dialer.Landline.Repo
  defp _repo_for("Wireless"), do: Dialer.Wireless.Repo
  defp _repo_for("wireless"), do: Dialer.Wireless.Repo
end
