defmodule Crm.Webhooks.ConsumerHelpers do
  @moduledoc """
  Helper functions for webhook consumers.
  """

  require Logger
  import Ecto.Query
  alias Crm.{Contact, Dial, Project, Repo}
  alias Dialer.{Landline, Wireless, ViciDial}
  alias Dialer.ViciDialSchemas.ViciDialList

  @doc """
  Gets the appropriate dialer repo based on the cluster name.
  """
  def get_dialer_repo("Wireless"), do: Wireless.Repo
  def get_dialer_repo("Landline"), do: Landline.Repo
  def get_dialer_repo(_), do: nil

  @doc """
  Validates that the project exists.
  """
  def validate_project(project_id) when is_binary(project_id) do
    case Integer.parse(project_id) do
      {id, _} -> validate_project(id)
      :error -> {:error, "Invalid project ID format"}
    end
  end

  def validate_project(project_id) when is_integer(project_id) do
    case Repo.get(Project, project_id) do
      nil -> {:error, "Project not found"}
      project -> {:ok, project}
    end
  end

  @doc """
  Creates a contact record from the lead message.
  """
  def create_contact(message) do
    {project_id, _} = Integer.parse(message.project_id)

    contact_params = %{
      project_id: project_id,
      full_name: message.full_name,
      first_name: message.first_name,
      last_name: message.last_name,
      email: message.email,
      home_add1: message.address_1,
      home_add2: message.address_2,
      home_city: message.city,
      home_state: message.state,
      home_post_code: message.post_code,
      home_phone: message.phone_number,
      company_name: message.company_name,
      source_code: message.source_code,
      new31: message.misc_data_1,
      new32: message.misc_data_2,
      new33: message.misc_data_3
    }

    # Get the next contact ID
    contact_id = get_next_contact_id()

    contact_params =
      contact_params
      |> Map.put(:contact_id, contact_id)
      |> Map.put(:dial_id, contact_id)

    %Contact{}
    |> Contact.changeset(contact_params)
    |> Repo.insert()
  end

  @doc """
  Creates a dial record for the contact.
  """
  def create_dial(contact, crc \\ "ADDED") do
    # Convert Decimal project_id to integer
    project_id = case contact.project_id do
      %Decimal{} -> Decimal.to_integer(contact.project_id)
      id when is_integer(id) -> id
      _ -> nil
    end

    dial_params = %{
      dial_id: contact.contact_id,
      source_table: "contact",
      source_field: "ContactID",
      source_id: to_string(contact.contact_id),
      phone_num: contact.home_phone || "",
      create_date_time: NaiveDateTime.utc_now(),
      crc: crc,
      project_id: project_id
    }

    %Dial{}
    |> Dial.changeset(dial_params)
    |> Repo.insert()
  end

  @doc """
  Creates a ViciDialList record for the contact.
  """
  def create_vicidial_list(dialer_repo, contact, project) do
    # Get the campaign ID for the project based on the dialer
    campaign_id = Project.campaign_id(project)

    # Check if the list exists, create it if not
    list_id = case contact.project_id do
      %Decimal{} -> Decimal.to_integer(contact.project_id)
      id when is_integer(id) -> id
      _ -> nil
    end
    list_name = "Project #{list_id}"

    # Get the configured ViciDial module (allows for testing)
    vici_dial_module = Application.get_env(:crm, :vici_dial_module, Dialer.ViciDial)

    # Ensure the list exists
    case vici_dial_module.add_list(dialer_repo, campaign_id, list_id, list_name) do
      {:ok, _} -> :ok
      {:error, %{errors: [list_id: {"has already been taken", _}]}} -> :ok
      {:error, reason} -> {:error, reason}
    end

    # Create the lead in the ViciDialList
    # Handle name fields - if first_name or last_name is missing, try to split full_name
    {first_name, last_name} = case {contact.first_name, contact.last_name, contact.full_name} do
      {first, last, _} when is_binary(first) and is_binary(last) and first != "" and last != "" ->
        {first, last}
      {_, _, full_name} when is_binary(full_name) and full_name != "" ->
        case split_full_name(full_name) do
          {first, last} -> {first, last}
          _ -> {full_name, ""} # If splitting fails, put full name in first_name
        end
      _ ->
        {"", ""} # Default to empty strings if no name data available
    end

    lead_params = %{
      entry_date: NaiveDateTime.utc_now(),
      status: "ADDED",
      vendor_lead_code: to_string(contact.contact_id),
      source_id: contact.source_code,
      list_id: list_id,
      phone_code: "1",
      phone_number: contact.home_phone || "",
      title: contact.title,
      first_name: first_name,
      last_name: last_name,
      address1: contact.home_add1,
      address2: contact.home_add2,
      city: contact.home_city,
      state: contact.home_state,
      postal_code: contact.home_post_code,
      email: contact.email
    }

    # Insert using the schema
    %Dialer.ViciDialSchemas.ViciDialList{}
    |> Dialer.ViciDialSchemas.ViciDialList.changeset(lead_params)
    |> dialer_repo.insert()
    |> case do
      {:ok, _} -> :ok
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Updates a contact with new misc data.
  """
  def update_contact_misc_data(contact_id, misc_data) do
    case Repo.get(Contact, contact_id) do
      nil ->
        {:error, "Contact not found"}

      contact ->
        contact
        |> Contact.changeset(%{
          new31: misc_data.misc_data_1 || contact.new31,
          new32: misc_data.misc_data_2 || contact.new32,
          new33: misc_data.misc_data_3 || contact.new33
        })
        |> Repo.update()
    end
  end

  @doc """
  Updates a dial record's CRC.
  """
  def update_dial_crc(dial_id, crc) do
    case Repo.get(Dial, dial_id) do
      nil ->
        {:error, "Dial record not found"}

      dial ->
        dial
        |> Dial.changeset(%{crc: crc})
        |> Repo.update()
    end
  end

  @doc """
  Updates a ViciDialList record's status.
  """
  def update_vicidial_list_status(dialer_repo, contact_id, status) do
    import Ecto.Query

    query = from l in Dialer.ViciDialSchemas.ViciDialList,
            where: l.vendor_lead_code == ^to_string(contact_id),
            update: [set: [status: ^status]]

    case dialer_repo.update_all(query, []) do
      {0, _} -> {:error, "ViciDialList record not found"}
      {_, _} -> :ok
    end
  end

  @doc """
  Finds a contact by project_id and full_name.
  """
  def find_contact(project_id, full_name) do
    Contact
    |> where([c], c.project_id == ^project_id and c.full_name == ^full_name)
    |> order_by([c], desc: c.contact_id)
    |> limit(1)
    |> Repo.one()
  end

  # Private functions

  defp get_next_contact_id do
    case Repo.one(from c in Contact, select: max(c.contact_id)) do
      nil -> 1
      max_id -> max_id + 1
    end
  end

  # Splits a full name into first and last name.
  # The split takes place at the first space character.
  # Everything before the space lands in the first name, and everything after in the last name.
  defp split_full_name(full_name) when is_binary(full_name) do
    case String.split(full_name, " ", parts: 2) do
      [first_name, last_name] ->
        {first_name, last_name}

      [first_name] ->
        {first_name, ""}

      _ ->
        {:error, "Could not split full name"}
    end
  end

  defp split_full_name(_), do: {:error, "Invalid full name format"}
end
