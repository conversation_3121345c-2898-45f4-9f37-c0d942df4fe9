defmodule Crm.Project do
  use Ecto.Schema
  use Admin.Paginate,
    repo: Crm.Repo,
    sortable: [:id, :name, :active, :dialer, :campaign_type],
    filterable: [:id, :name, :parent_id, :description, :active, :dialer, :campaign_type, :landline_campaign_id, :wireless_campaign_id]

  import Ecto.Changeset

  @moduledoc """
    This module provides the schema and common functions for the CoreKPIs for the CRM.
  """

  @all_fields [
    :name,
    :description,
    :active,
    :dialer,
    :max_attempts,
    :parent_id,
    :landline_campaign_id,
    :wireless_campaign_id,
    :campaign_type
  ]
  @primary_key {:id, :id, autogenerate: true, source: :ProjectID}

  schema "project" do
    field :name, :string, source: :ProjectName
    field :description, :string, source: :Description, default: ""
    field :active, :string, source: :Active, default: "1"
    field :dialer, :string, source: :Dialer
    field :max_attempts, :integer, source: :MaxAttempts
    field :parent_id, :integer, source: :ParentID
    field :client_id, :integer, source: :ClientID
    field :landline_campaign_id, :string, source: :LandlineCampaignID
    field :wireless_campaign_id, :string, source: :WirelessCampaignID
    field :campaign_type, :string, source: :CampaignType
    field :sensitive_information, :string, source: :SensitiveInformation
    field :dial_method, :string, source: :DialMethod
  end

  @doc false
  def changeset(project, attrs) do
    project
    |> cast(attrs, @all_fields)
    |> validate_required([:name])
  end

  def dialer(%{dialer: "Landline"}), do: :landline
  def dialer(%{dialer: "Wireless"}), do: :wireless

  def campaign_id(%{dialer: "Landline", landline_campaign_id: id}), do: id
  def campaign_id(%{dialer: "Wireless", wireless_campaign_id: id}), do: id

  def campaign_id(project) do
    project
    |> Map.get(:landline_campaign_id)
    |> case do
      nil -> project.wireless_campaign_id
      "" -> project.wireless_campaign_id
      id -> id
    end
  end
end
