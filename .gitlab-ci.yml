variables:
  MIX_ENV: prod
  BUILD_OUTPUT_DIR: _build/prod/rel/admin
  CONTAINER_DOCS_IMAGE: $CI_REGISTRY_IMAGE:latest-docs
  GIT_SHA: $CI_COMMIT_SHA

stages:
  - deps
  - build
  - docs
  - buildContainer
  - releaseContainer

default:
  artifacts:
    expire_in: 1h

cache:
  - key:
      files:
        - mix.exs
        - mix.lock
    paths:
      - deps/
      - _build/

build:deps:
  stage: deps
  image: gitlab.gad-inc.com:5050/integration/elixir-builder:v1.18.2
  script:
    - mix deps.get
    - mix deps.compile
  cache:
    key:
      files:
        - mix.exs
        - mix.lock
    paths:
      - deps/
      - _build/

build:app:
  stage: build
  image: gitlab.gad-inc.com:5050/integration/elixir-builder:v1.18.2
  script:
    - mix compile
    - mix assets.deploy
    - mix release --overwrite
  dependencies:
    - build:deps
  artifacts:
    paths:
      - $BUILD_OUTPUT_DIR
  only:
    - staging
    - tags

build:docs:
  stage: build
  image: gitlab.gad-inc.com:5050/integration/elixir-builder:v1.18.2
  allow_failure: true  # Make this job optional
  only:
   - tags
  script:
    - mix deps.get
    - mix compile
    - mix docs
  dependencies:
    - build:deps
  artifacts:
    paths:
      - doc/

image:docs:
  stage: buildContainer
  cache: []
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  allow_failure: true  # Make this job optional
  only:
    - tags
  dependencies:
    - build:docs
  environment:
    name: admin-docs
    action: prepare
    url: https://admin-docs.apps-2.gad-inc.com

  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/docs.Dockerfile"
      --destination "${CONTAINER_DOCS_IMAGE}"

image:app:
  stage: buildContainer
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --build-arg "CI_COMMIT_SHA=${GIT_SHA}"
      --destination "${CI_REGISTRY_IMAGE}:latest"
      --destination "${CI_REGISTRY_IMAGE}:staging"
  dependencies:
    - build:app
  only:
    - staging

image:release-app:
  stage: releaseContainer
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --build-arg "CI_COMMIT_SHA=${GIT_SHA}"
      --destination "${CI_REGISTRY_IMAGE}:prod"
      --destination "${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG}"
  dependencies:
    - build:app
  only:
    - tags
