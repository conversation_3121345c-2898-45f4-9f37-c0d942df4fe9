defmodule Admin.AdminRepo.Migrations.AddTenantTable do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:gorgias_tenants, primary_key: false, prefix: "metrics") do
      add :id, :bigserial, null: false, primary_key: true
      add :name, :text
      add :authorization_token, :text
      add :subdomain, :text
      add :schema_name, :text
      add :created_datetime, :utc_datetime
      add :updated_datetime, :utc_datetime
      add :active, :boolean
    end
  end

  def down do
    drop table(:gorgias_tenants, prefix: "metrics")
  end
end
