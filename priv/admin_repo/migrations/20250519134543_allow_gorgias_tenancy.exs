defmodule Admin.AdminRepo.Migrations.AllowGorgiasTenancy do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:gorgias_tenants, prefix: "metrics") do
      modify :schema_name, :text, null: false
    end
  end

  def down do
    alter table(:gorgias_tenants, prefix: "metrics") do
      modify :schema_name, :text, null: true
    end
  end
end
