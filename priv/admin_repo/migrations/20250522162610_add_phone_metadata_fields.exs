defmodule Admin.AdminRepo.Migrations.AddPhoneMetadataFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:contact_staging) do
      add :homephone_metadata, :map
      add :companyphone_metadata, :map
      add :newphone_metadata, :map
      add :altcompanyphone_metadata, :map
    end
  end

  def down do
    alter table(:contact_staging) do
      remove :altcompanyphone_metadata
      remove :newphone_metadata
      remove :companyphone_metadata
      remove :homephone_metadata
    end
  end
end
