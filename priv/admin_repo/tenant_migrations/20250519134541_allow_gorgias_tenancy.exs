defmodule Admin.AdminRepo.TenantMigrations.AllowGorgiasTenancy do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:gorgias_users, primary_key: false, prefix: prefix()) do
      add :id, :bigint, null: false, primary_key: true
      add :active, :boolean
      add :bio, :text
      add :created_datetime, :utc_datetime
      add :deactivated_datetime, :utc_datetime
      add :country, :text
      add :email, :text
      add :external_id, :text
      add :firstname, :text
      add :lastname, :text
      add :meta, :map
      add :name, :text
      add :role, :map
      add :timezone, :text
      add :updated_datetime, :utc_datetime
    end

    create unique_index(:gorgias_users, [:email], name: "gorgias_users_email_index")

    create table(:gorgias_tickets, primary_key: false, prefix: prefix()) do
      add :id, :bigint, null: false, primary_key: true
      add :channel, :text
      add :opened_datetime, :utc_datetime
      add :created_datetime, :utc_datetime
      add :updated_datetime, :utc_datetime
      add :trashed_datetime, :utc_datetime
      add :closed_datetime, :utc_datetime
      add :snooze_datetime, :utc_datetime
      add :external_id, :text
      add :meta, :map
      add :spam, :boolean
      add :subject, :text
      add :status, :text
      add :via, :text
      add :uri, :text
      add :gorgias_customer_id, :bigint
      add :shopify_order_id, :bigint
      add :shopify_order_name, :text
      add :shopify_customer_id, :bigint
      add :shopify_customer_id_looked_up, :boolean, default: false
      add :satisfaction_survey, :map
      add :assignee_team, :text
    end

    create table(:gorgias_ticket_tags, primary_key: false, prefix: prefix()) do
      add :id, :bigserial, null: false, primary_key: true

      add :ticket_id,
          references(:gorgias_tickets,
            column: :id,
            name: "gorgias_ticket_tags_ticket_id_fkey",
            type: :bigint,
            prefix: prefix()
          )

      add :tag_id, :bigint
    end

    create table(:gorgias_ticket_messages, primary_key: false, prefix: prefix()) do
      add :id, :bigint, null: false, primary_key: true
      add :body_text, :text
      add :channel, :text
      add :created_datetime, :utc_datetime
      add :external_id, :text
      add :failed_datetime, :utc_datetime
      add :from_agent, :boolean
      add :integration_id, :bigint
      add :message_id, :text
      add :rule_id, :bigint
      add :sent_datetime, :utc_datetime
      add :subject, :text

      add :ticket_id,
          references(:gorgias_tickets,
            column: :id,
            name: "gorgias_ticket_messages_ticket_id_fkey",
            type: :bigint,
            prefix: prefix()
          )

      add :via, :text
      add :uri, :text
    end

    create table(:gorgias_ticket_custom_fields, primary_key: false, prefix: prefix()) do
      add :id, :bigserial, null: false, primary_key: true
      add :value, :text

      add :ticket_id,
          references(:gorgias_tickets,
            column: :id,
            name: "gorgias_ticket_custom_fields_ticket_id_fkey",
            type: :bigint,
            prefix: prefix()
          )

      add :custom_field_id, :bigint
    end

    create table(:gorgias_teams, primary_key: false, prefix: prefix()) do
      add :id, :bigint, null: false, primary_key: true
      add :uri, :text
      add :name, :text
      add :description, :text
      add :created_datetime, :utc_datetime
    end

    create table(:gorgias_tags, primary_key: false, prefix: prefix()) do
      add :id, :bigserial, null: false, primary_key: true
    end

    alter table(:gorgias_ticket_tags, prefix: prefix()) do
      modify :tag_id,
             references(:gorgias_tags,
               column: :id,
               name: "gorgias_ticket_tags_tag_id_fkey",
               type: :bigint,
               prefix: prefix()
             )
    end

    alter table(:gorgias_tags, prefix: prefix()) do
      add :name, :text, null: false
      add :tag_color, :text
    end

    create unique_index(:gorgias_tags, [:name], name: "gorgias_tags_name_index")

    create table(:gorgias_state, primary_key: false, prefix: prefix()) do
      add :id, :bigserial, null: false, primary_key: true
      add :name, :text, null: false
      add :cursor, :text
    end

    create unique_index(:gorgias_state, [:name], name: "gorgias_state_id_index")

    create table(:gorgias_events, primary_key: false, prefix: prefix()) do
      add :id, :bigint, null: false, primary_key: true
      add :context, :text
      add :channel, :text
      add :created_datetime, :utc_datetime
      add :data, :map
      add :object_id, :bigint
      add :object_type, :text
      add :type, :text
      add :user_id, :bigint
      add :uri, :text
    end

    create table(:gorgias_custom_fields, primary_key: false, prefix: prefix()) do
      add :id, :bigserial, null: false, primary_key: true
    end

    alter table(:gorgias_ticket_custom_fields, prefix: prefix()) do
      modify :custom_field_id,
             references(:gorgias_custom_fields,
               column: :id,
               name: "gorgias_ticket_custom_fields_custom_field_id_fkey",
               type: :bigint,
               prefix: prefix()
             )
    end

    create unique_index(:gorgias_ticket_custom_fields, [:ticket_id, :custom_field_id],
             name: "gorgias_ticket_custom_fields_ticket_customfield_index"
           )

    alter table(:gorgias_custom_fields, prefix: prefix()) do
      add :object_type, :text, null: false
      add :label, :text
      add :description, :text
      add :priority, :bigint
      add :required, :boolean
    end

    create unique_index(:gorgias_custom_fields, [:label],
             name: "gorgias_custom_fields_label_index"
           )

    create table(:gorgias_csat_surveys, primary_key: false, prefix: prefix()) do
      add :id, :bigint, null: false, primary_key: true
      add :body_text, :text
      add :created_datetime, :utc_datetime
      add :customer_id, :bigint
      add :meta, :map
      add :score, :bigint
      add :scored_datetime, :utc_datetime
      add :sent_datetime, :utc_datetime
      add :should_send_datetime, :utc_datetime
      add :ticket_id, :bigint
      add :uri, :text
    end

    create unique_index(:gorgias_csat_surveys, [:ticket_id],
             name: "gorgias_csat_surveys_ticket_id_index"
           )
  end

  def down do
    drop_if_exists unique_index(:gorgias_csat_surveys, [:ticket_id],
                     name: "gorgias_csat_surveys_ticket_id_index"
                   )

    drop table(:gorgias_csat_surveys, prefix: prefix())

    drop_if_exists unique_index(:gorgias_custom_fields, [:label],
                     name: "gorgias_custom_fields_label_index"
                   )

    alter table(:gorgias_custom_fields, prefix: prefix()) do
      remove :required
      remove :priority
      remove :description
      remove :label
      remove :object_type
    end

    drop_if_exists unique_index(:gorgias_ticket_custom_fields, [:ticket_id, :custom_field_id],
                     name: "gorgias_ticket_custom_fields_ticket_customfield_index"
                   )

    drop constraint(
           :gorgias_ticket_custom_fields,
           "gorgias_ticket_custom_fields_custom_field_id_fkey"
         )

    alter table(:gorgias_ticket_custom_fields, prefix: prefix()) do
      modify :custom_field_id, :bigint
    end

    drop table(:gorgias_custom_fields, prefix: prefix())

    drop table(:gorgias_events, prefix: prefix())

    drop_if_exists unique_index(:gorgias_state, [:name], name: "gorgias_state_id_index")

    drop table(:gorgias_state, prefix: prefix())

    drop_if_exists unique_index(:gorgias_tags, [:name], name: "gorgias_tags_name_index")

    alter table(:gorgias_tags, prefix: prefix()) do
      remove :tag_color
      remove :name
    end

    drop constraint(:gorgias_ticket_tags, "gorgias_ticket_tags_tag_id_fkey")

    alter table(:gorgias_ticket_tags, prefix: prefix()) do
      modify :tag_id, :bigint
    end

    drop table(:gorgias_tags, prefix: prefix())

    drop table(:gorgias_teams, prefix: prefix())

    drop constraint(:gorgias_ticket_custom_fields, "gorgias_ticket_custom_fields_ticket_id_fkey")

    drop table(:gorgias_ticket_custom_fields, prefix: prefix())

    drop constraint(:gorgias_ticket_messages, "gorgias_ticket_messages_ticket_id_fkey")

    drop table(:gorgias_ticket_messages, prefix: prefix())

    drop constraint(:gorgias_ticket_tags, "gorgias_ticket_tags_ticket_id_fkey")

    drop table(:gorgias_ticket_tags, prefix: prefix())

    drop table(:gorgias_tickets, prefix: prefix())

    drop_if_exists unique_index(:gorgias_users, [:email], name: "gorgias_users_email_index")

    drop table(:gorgias_users, prefix: prefix())
  end
end
