defmodule Admin.AdminRepo.TenantMigrations.AllowGorgiasTenancy do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:gorgias_ticket_custom_fields, "gorgias_ticket_custom_fields_ticket_id_fkey")

    drop constraint(:gorgias_ticket_messages, "gorgias_ticket_messages_ticket_id_fkey")

    drop constraint(:gorgias_ticket_tags, "gorgias_ticket_tags_ticket_id_fkey")
  end

  def down do
    create table(:gorgias_ticket_custom_fields, primary_key: false, prefix: prefix()) do
      modify :ticket_id,
          references(:gorgias_tickets,
            column: :id,
            name: "gorgias_ticket_custom_fields_ticket_id_fkey",
            type: :bigint,
            prefix: prefix()
          )
    end

    create table(:gorgias_ticket_messages, primary_key: false, prefix: prefix()) do
      modify :ticket_id,
          references(:gorgias_tickets,
            column: :id,
            name: "gorgias_ticket_messages_ticket_id_fkey",
            type: :bigint,
            prefix: prefix()
          )
    end

    alter table(:gorgias_ticket_tags, prefix: prefix()) do
      modify :ticket_id,
          references(:gorgias_tickets,
            column: :id,
            name: "gorgias_ticket_tags_ticket_id_fkey",
            type: :bigint,
            prefix: prefix()
          )
    end

  end
end
