{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": true, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "tag_color", "type": "text"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "DD99424C395A49A54517FB7FEB7C389EF96E60668756C3480C3AD4B356EDDE55", "identities": [{"base_filter": null, "index_name": "gorgias_tags_name_index", "keys": ["name"], "name": "name"}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Admin.AdminRepo", "schema": "metrics", "table": "gorgias_tags"}