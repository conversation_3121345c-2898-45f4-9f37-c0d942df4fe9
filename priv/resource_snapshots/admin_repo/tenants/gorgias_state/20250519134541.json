{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": true, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "cursor", "type": "text"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "BC4B51749B12A1AFA5E35A0370BCD993084BBC74D4B1C5EE1F32B7BC185B3B47", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "gorgias_state_id_index", "keys": [{"type": "atom", "value": "name"}], "name": "id", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "repo": "Elixir.Admin.AdminRepo", "schema": null, "table": "gorgias_state"}