{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": true, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "name": "gorgias_ticket_tags_ticket_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "gorgias_tickets"}, "size": null, "source": "ticket_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "name": "gorgias_ticket_tags_tag_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "gorgias_tags"}, "size": null, "source": "tag_id", "type": "bigint"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "85D385A8AA742FEA641FC1BA52B70C014B97B11AF22C36F9A4889F37B1953AA5", "identities": [], "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "repo": "Elixir.Admin.AdminRepo", "schema": null, "table": "gorgias_ticket_tags"}