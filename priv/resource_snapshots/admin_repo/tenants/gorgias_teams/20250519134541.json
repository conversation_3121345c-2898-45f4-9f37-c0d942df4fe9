{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "uri", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "created_datetime", "type": "utc_datetime"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "A14EBBCFC5056E430F851E2DAFBACAD92C3FBA7E972EC932867B847B98A1995E", "identities": [], "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "repo": "Elixir.Admin.AdminRepo", "schema": null, "table": "gorgias_teams"}