{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": true, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "value", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "name": "gorgias_ticket_custom_fields_ticket_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "gorgias_tickets"}, "size": null, "source": "ticket_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "name": "gorgias_ticket_custom_fields_custom_field_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "gorgias_custom_fields"}, "size": null, "source": "custom_field_id", "type": "bigint"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "C2544946202F2C97B23A60E600BD56E875BB439E6C271833E41122E1E03362D1", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "gorgias_ticket_custom_fields_ticket_customfield_index", "keys": [{"type": "atom", "value": "ticket_id"}, {"type": "atom", "value": "custom_field_id"}], "name": "ticket_customfield", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "repo": "Elixir.Admin.AdminRepo", "schema": null, "table": "gorgias_ticket_custom_fields"}