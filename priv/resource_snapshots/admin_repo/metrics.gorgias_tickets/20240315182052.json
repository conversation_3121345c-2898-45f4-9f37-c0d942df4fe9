{"attributes": [{"default": "nil", "size": null, "type": "bigint", "source": "id", "references": null, "allow_nil?": false, "generated?": false, "primary_key?": true}, {"default": "nil", "size": null, "type": "text", "source": "channel", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "utc_datetime", "source": "opened_datetime", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "utc_datetime", "source": "created_datetime", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "utc_datetime", "source": "updated_datetime", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "utc_datetime", "source": "trashed_datetime", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "utc_datetime", "source": "closed_datetime", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "utc_datetime", "source": "snooze_datetime", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "external_id", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "map", "source": "meta", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "boolean", "source": "spam", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "subject", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "status", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "via", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "uri", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "bigint", "source": "gorgias_customer_id", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "bigint", "source": "shopify_order_id", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "shopify_order_name", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "bigint", "source": "shopify_customer_id", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "false", "size": null, "type": "boolean", "source": "shopify_customer_id_looked_up", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "map", "source": "satisfaction_survey", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "assignee_team", "references": null, "allow_nil?": true, "generated?": false, "primary_key?": false}], "table": "gorgias_tickets", "hash": "7957CB6F7242EE65505C9DE22733CA4599C54879FCD055B547A016DB012651BD", "identities": [], "repo": "Elixir.Admin.AdminRepo", "schema": "metrics", "multitenancy": {"global": null, "attribute": null, "strategy": null}, "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true}