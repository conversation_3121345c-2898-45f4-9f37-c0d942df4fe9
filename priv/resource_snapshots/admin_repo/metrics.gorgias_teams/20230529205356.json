{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "uri", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "created_datetime", "type": "utc_datetime"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "49CC8E4C18CDA18CB4B75D0EFCA26575BCA78E7D4419501C8C2343151763D587", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Admin.AdminRepo", "schema": "metrics", "table": "gorgias_teams"}