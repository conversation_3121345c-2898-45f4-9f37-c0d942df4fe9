{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": true, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "gorgias_ticket_tags_ticket_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "metrics", "table": "gorgias_tickets"}, "size": null, "source": "ticket_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "gorgias_ticket_tags_tag_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "metrics", "table": "gorgias_tags"}, "size": null, "source": "tag_id", "type": "bigint"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "A9888921A2CA5D83630E2C537B8312C8F37F5DF78B3C2FC9DBD3EA91C7A69F49", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Admin.AdminRepo", "schema": "metrics", "table": "gorgias_ticket_tags"}