{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "body_text", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "created_datetime", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "customer_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "meta", "type": "map"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "score", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "scored_datetime", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "sent_datetime", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "should_send_datetime", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "ticket_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "uri", "type": "text"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "2A3A626B5A53B3B3E12F91E6D8E6127334557CA6B633811356994C52FE5E06A3", "identities": [{"base_filter": null, "index_name": "gorgias_csat_surveys_ticket_id_index", "keys": ["ticket_id"], "name": "ticket_id"}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Admin.AdminRepo", "schema": "metrics", "table": "gorgias_csat_surveys"}